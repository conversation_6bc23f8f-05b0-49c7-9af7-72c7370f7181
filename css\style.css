/********** Template CSS **********/
:root {
    --primary: #273675;
    --secondary: #1e73b2;
    --light: #EFFDF5;
    --dark: #2B3940;
    --yellow: #fdc05c;
}


.londrina-sketch-regular {
    font-family: "<PERSON><PERSON><PERSON>", sans-serif;
  }

  .mon-font {
    font-family: 'Montserrat', sans-serif;
}  h1, h2, h3, h4{
    font-family: 'D-DIN', sans-serif;
    font-weight: bold;
}

P{
    font-family: 'Montserrat', sans-serif;
    font-size: large;
}


.home{
    background: linear-gradient(to right, #273675, #1e73b2);;
}

.f-14 {
    font-size: 12px;
  }
.f-13 {
    font-size: 7px;
  }
.font-p{
    font-size: 27px;
}
.font-a{
    font-size: 35px;
}
.font-b{
    font-size: 20px;
}
 .w-50{
    width: 70%;
 }
 .py-20{
    margin-top: 200px;
 }
 .px-20{
    margin-left: 100px;
 }
 .pz-20{
    margin-right: 100px;
 }
.back-to-top {
    position: fixed;
    display: none;
    right: 45px;
    bottom: 45px;
    z-index: 99;
}
.text-yellow{
    color: var(--yellow);
}
.text-black{
    color: #000;
}
.mt-100{
    margin-top: 120px;
}
.mt-5{
    margin-top: 100px;
}
.text-end{
   text-align: end;
}
.text-blue{
    color: var(--primary);
}
.ml{
    margin-left: 10px;
}
.mb-7{
    margin-bottom: 50px;
}
.yes{

    color: white;
    display: inline-block;
}
.timer {
    background-color: #343a40;
    color: white;
    padding: 20px;
    margin: 10px;
    border-radius: 5px;
    font-size: 24px;
    display: inline-block;
    width: 100px;
    text-align: center;
}
.row1 {
    margin-top: 50px;
    display: flex;
    justify-content: center; /* Center timers horizontally */
}
/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}

@media (min-width: 991.98px) {
    .facts {
        width: 60%;
        position: relative;
        margin-top: -140px;
        z-index: 1;
    }
}
.facts .d-flex {
    flex-wrap: wrap; /* Allow wrapping */
}

.facts .text-center {
    flex: 1 0 calc(25% - 1rem); /* Four items in a row with spacing */
    margin: 0.1rem; /* Margin between items */
}

@media (max-width: 768px) {
    .facts .text-center {
        flex: 1 0 calc(50% - 1rem); /* Two items in a row on smaller screens */
    }
}

@media (max-width: 480px) {
    .facts .text-center {
        flex: 1 0 100%; /* One item in a row on very small screens */
    }
}
/*** Button ***/
.btn {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    transition: .5s;
    text-transform: uppercase;
}

.btn.btn-primary,
.btn.btn-secondary {
    color: #FFFFFF;
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-lg-square {
    width: 48px;
    height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    border-radius: 2px;
}


/*** Navbar ***/
.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

.navbar-light .navbar-nav .nav-link {
    margin-right: 30px;
    padding: 25px 0;
    color: #FFFFFF;
    font-size: 15px;
    text-transform: uppercase;
    outline: none;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary);
}

@media (max-width: 991.98px) {
    .navbar-light .navbar-nav .nav-link  {
        margin-right: 0;
        padding: 10px 0;
    }

    .navbar-light .navbar-nav {
        border-top: 1px solid #EEEEEE;
    }
}

.navbar-light .navbar-brand,
.navbar-light a.btn {
    height: 75px;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--dark);
    font-weight: 500;
}

.navbar-light.sticky-top {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: .5s;
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        margin-top: 0;
        opacity: 0;
        visibility: hidden;
        transition: .5s;
    }

    .navbar .dropdown-menu.fade-down {
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
    }

    .navbar .nav-item:hover .dropdown-menu {
        top: 100%;
        transform: rotateX(0deg);
        visibility: visible;
        transition: .5s;
        opacity: 1;
    }
}


/*** Header ***/
.header-carousel .container,
.page-header .container {
    position: relative;
    padding: 45px 0 45px 35px;
}

.header-carousel .container::before,
.header-carousel .container::after,
.page-header .container::before,
.page-header .container::after {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 100px;
    height: 15px;
}

.header-carousel .container::after,
.page-header .container::after {
    top: 100%;
    margin-top: -15px;
}

@media (max-width: 768px) {
    .header-carousel .owl-carousel-item {
        position: relative;
        min-height: 500px;
    }
    
    .header-carousel .owl-carousel-item img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .header-carousel .owl-carousel-item p {
        font-size: 14px !important;
        font-weight: 400 !important;
    }

    .header-carousel .owl-carousel-item h1 {
        font-size: 30px;
        font-weight: 600;
    }
}

.header-carousel .owl-nav {
    position: absolute;
    top: 50%;
    right: 8%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
}

.header-carousel .owl-nav .owl-prev,
.header-carousel .owl-nav .owl-next {
    margin: 7px 0;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    background: transparent;
    border: 1px solid #FFFFFF;
    border-radius: 2px;
    font-size: 22px;
    transition: .5s;
}

.header-carousel .owl-nav .owl-prev:hover,
.header-carousel .owl-nav .owl-next:hover {
    background: var(--yellow);
    border-color: var(--yellow);
}


.page-header {
    background: linear-gradient(rgba(43, 57, 64, .5), rgba(43, 57, 64, .5)), url(../img/Links/2151480196.jpg) center center no-repeat;
    height: 70vh;
    background-size: cover;
}





.breadcrumb-item + .breadcrumb-item::before {
    color: var(--light);
}


/* home section */
#home {
    background: 
  linear-gradient(rgba(20, 94, 138, 0.555), transparent), 
  url(../img/banner.jpg) center center no-repeat;
    background-size:cover; /* Cover the entire section */
    background-position: right center; /* Center the image */
    min-height: 400px; /* Set a minimum height */
    display: flex; /* Flexbox for centering content */
    align-items: center; /* Vertically center content */
    justify-content: center; /* Horizontally center content */
    padding-top: 220px;
    padding-bottom: 100px;
    color: white; /* Text color for better visibility */
}

@media (max-width: 768px) {
    #home {
        padding-top: 150px; /* Adjust top padding for smaller screens */
        padding-bottom: 50px; /* Adjust bottom padding for smaller screens */
    }
}

@media (max-width: 480px) {
    #home {
        min-height: 300px; /* Adjust min-height for very small screens */
        padding-top: 100px; /* Further adjust padding */
        padding-bottom: 30px; /* Further adjust padding */
    }
}


#home h1 {
	color: #f2af00;
	font-weight: 300;
	padding-bottom: 10px;
    font-family: 'MontserratSemiBold', sans-serif;
    font-weight: bold;
}
#home h2 {
	line-height: 10px;
    font-family: 'MontserratSemiBold', sans-serif;
}
#home .btn {
	border: 2px solid #303030;
	border-radius: 0;
    background-color: var(--yellow);
	color: var(--dark);
    text-transform: uppercase;
	font-family: 'Raleway', sans-serif;
	font-weight: bold;
	padding-top: 14px;
	width: 200px;
	height: 50px;
	margin-top: 20px;
    margin-bottom: 40px;

}
#home .btn:hover {
	background: var(--yellow);
	border-color: transparent;
	color: #fff;
}
@media (max-width: 768px) {
    h1 {
        font-size: 1.5rem;
    }
    p {
        font-size: 0.9rem;
    }
    img.img-fluid {
        max-width: 100%;
        height: auto;
    }
}

@media (min-width: 769px) {
    img.img-fluid {
        max-width: 80%;
        height: auto;
    }
    .container, .container-fluid {
        padding-left: 5%;
        padding-right: 5%;
    }
    .row {
        margin-left: -2%;
        margin-right: -2%;
    }
    .col-lg-6, .col-md-6 {
        padding-left: 2%;
        padding-right: 2%;
    }
}


/* service section */
#service {
	padding-top: 0px;
	padding-bottom: 0px;
}
#service h2 {
	border-left: 2px solid #f2af00;
	display: inline-block;
	padding: 20px;
}
#service .fa {
	border-top: 4px solid #f2af00;
	border-bottom: 4px solid #dc5034;
	border-radius: 50%;
	font-size: 26px;
	width: 90px;
	height: 90px;
	line-height: 90px;
	text-align: center;
	vertical-align: middle;
	margin-top: 60px;
	margin-bottom: 10px;
}
/*** About ***/
.about-bg {
    background-image: -webkit-repeating-radial-gradient(center center, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2) 1px, transparent 1px, transparent 100%);
    background-image: -moz-repeating-radial-gradient(center center, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2) 1px, transparent 1px, transparent 100%);
    background-image: -ms-repeating-radial-gradient(center center, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2) 1px, transparent 1px, transparent 100%);
    background-image: -o-repeating-radial-gradient(center center, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2) 1px, transparent 1px, transparent 100%);
    background-image: repeating-radial-gradient(center center, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2) 1px, transparent 1px, transparent 100%);
    background-size: 5px 5px;
}


/*** Category ***/
.cat-item {
    display: block;
    box-shadow: 0 0 45px rgba(0, 0, 0, .08);
    border: 1px solid transparent;
    transition: .5s;
}

.cat-item:hover {
    border-color: rgba(0, 0, 0, .08);
    box-shadow: none;
}


/*** Job Listing ***/
.nav-pills .nav-item .active {
    border-bottom: 2px solid var(--primary);
}

.job-item {
    border: 1px solid transparent;
    border-radius: 2px;
    box-shadow: 0 0 45px rgba(0, 0, 0, .08);
    transition: .5s;
}

.job-item:hover {
    border-color: rgba(0, 0, 0, .08);
    box-shadow: none;
}


/*** Testimonial ***/
.testimonial-carousel .owl-item .testimonial-item,
.testimonial-carousel .owl-item.center .testimonial-item * {
    transition: .5s;
}

.testimonial-carousel .owl-item.center .testimonial-item {
    background: var(--primary) !important;
}

.testimonial-carousel .owl-item.center .testimonial-item * {
    color: #FFFFFF !important;
}

.testimonial-carousel .owl-dots {
    margin-top: 24px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.testimonial-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 15px;
    height: 15px;
    border: 5px solid var(--primary);
    border-radius: 2px;
    transition: .5s;
}

.testimonial-carousel .owl-dot.active {
    background: var(--dark);
    border-color: var(--primary);
}

/* Testimonial Section Fixes - Prevent text truncation */
.testimonial-section {
    background: linear-gradient(to right, var(--primary), var(--secondary));
    min-height: auto;
    padding: 80px 0;
    overflow: visible;
}

/* Bootstrap Carousel Fixes for Testimonials */
#testimonialCarousel {
    min-height: auto;
    overflow: visible;
}

#testimonialCarousel .carousel-inner {
    min-height: auto;
    overflow: visible;
}

#testimonialCarousel .carousel-item {
    min-height: auto;
    padding: 20px 0;
    overflow: visible;
}

#testimonialCarousel .carousel-item .row {
    min-height: auto;
    margin: 0;
}

#testimonialCarousel .col-md-4 {
    padding: 20px 15px;
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

#testimonialCarousel .col-md-4 p {
    flex-grow: 1;
    line-height: 1.6;
    font-size: 16px;
    margin-bottom: 20px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

#testimonialCarousel .col-md-4 h5 {
    margin-top: auto;
    padding-top: 10px;
}

/* Responsive adjustments for testimonials */
@media (max-width: 768px) {
    .testimonial-section {
        padding: 60px 0;
    }
    
    #testimonialCarousel .col-md-4 {
        min-height: 250px;
        margin-bottom: 20px;
        padding: 15px 10px;
    }
    
    #testimonialCarousel .col-md-4 p {
        font-size: 15px;
        line-height: 1.5;
    }
}

@media (max-width: 576px) {
    #testimonialCarousel .col-md-4 {
        min-height: 220px;
        padding: 10px;
    }
    
    #testimonialCarousel .col-md-4 p {
        font-size: 14px;
        line-height: 1.4;
    }
}

/* Our Team
-------------------------------------------------------*/

.our-team .team-row {
    margin-left: -40px;
    margin-right: -40px;
    align-items: center;
  }
  
  .our-team .team-wrap {
    padding: 0 40px;
  }
  
  .our-team .container-fluid {
    padding: 0 50px;
  }
  
  .team-img img {
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    overflow: hidden;
    width: 100%;
  }
  
  .team-member,
  .team-img {
    position: relative;
    overflow: hidden;
  }
  
  .team-title {
    display: flex;
    margin: 30px 0 7px;
    text-align: start;
  }


  
  .team-title i {
    text-align: right;
    margin-left: 0.5em; /* Space between text and icon */
}
  
  .team-details {
    opacity: 0;
    position: absolute;
    top: 50%;
    left: 0;
    padding: 5%;
    overflow: hidden;
    width: 100%;
    z-index: 2;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
  }
  
  .team-details p {
    color: #fff;
  }
  
  .team-img:hover .team-details {
    opacity: 1;
    margin-top: -80px;
  }
  
  .team-img:hover .overlay {
    opacity: 1;
  }
  
  .socials a {
    display: inline-block;
    width: 37px;
    height: 37px;
    background-color: transparent;
  }
  
  .socials i {
    line-height: 37px;
    color: #616161;
    font-size: 14px;
    width: 37px;
    height: 37px;
    border-radius: 50%;
    text-align: center;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    -ms-transition: all 0.2s linear;
    transition: all 0.2s linear;
  }
  
  .team-details .socials i {
      color: #fff;
  }
  
  .socials a:hover i {
    color: #fff;
    background-color: #355c7d;
  }
  
  

/*============== Features Section =================*/
.feature_section{
	float:left;
	width:100%;
	background:#f8f8fa;
	padding:80px 0 80px 0;
	}

.feature_section .leftside{
	float:left;
	width:390px;
	margin:0 0 0 35px;
	}

.feature_section .leftside img{
	float:right;
	}

.feature_section .rightside{
	float:right;
	width:587px;
	margin:0 35px 0 0;
	}
	
.feature_section h2{
	text-align:left;
	font-family: 'open_sansregular';
	font-size:30px;
	color:#333;
	font-weight:normal;
	margin:0 0 30px 0;
	padding:0 0 0 0;
	}
	
.feature_section  p{
	text-align:left;
	font-family: 'open_sanslight';
	font-size:14px;
	color:#777779;
	line-height:24px;
	font-weight:normal;
	margin:0 0 30px 0;
	}
	
.feature{
	float:left;
	width:100%;
	text-align:left;
	font-family: 'open_sanslight';
	font-size:14px;
	color:#777779;
	line-height:24px;
	font-weight:normal;
	margin:0 0 25px 0;
	}

.feature span{
	float:left;
	width:33px;
	height:33px;
	background:#187cc2;
	text-align:center;
	font-size:16px;
	color:#fff;
	line-height:33px;
	transition:0.5s; -webkit-transition:0.5s;
	border-radius:3px; -webkit-border-radius:3px;
	margin:0 16px 0 0;
	}

.feature .description{
	float:left;
	width:530px;
	}

.feature strong{
	color:#333;
	font-family: 'open_sanssemibold';
	font-weight:normal;
	}

.feature.one{
	background:url(images/icons/i4.png) no-repeat left top;
	}
	
.feature.two{
	background:url(images/icons/i5.png) no-repeat left top;
	}

   
      
a.button3{
	float:left;
	width:145px;
	height:45px;
	border:1px solid #187cc2;
	text-align:center;
	font-family: 'open_sanslight';
	font-size:14px;
	color:#fff;
	font-weight:normal;
	text-transform:uppercase;
	line-height:45px;
	margin:10px 25px 0 0;
	border-radius:3px; -webkit-border-radius:3px; 
	transition:0.5s; -webkit-transition:0.5s;
	text-decoration:none;
	background:#187cc2;
	}
	
a.button3:hover{
	background:none;
	border:1px solid #000;
	text-decoration:none;
	color:#000;
	}
	
a.button4{
	float:left;
	width:145px;
	height:45px;
	border:1px solid #000;
	text-align:center;
	font-family: 'open_sanslight';
	font-size:14px;
	color:#000;
	font-weight:normal;
	text-transform:uppercase;
	line-height:45px;
	margin:10px 0 0 0;
	border-radius:3px; -webkit-border-radius:3px; 
	transition:0.5s; -webkit-transition:0.5s;
	background:none;
	text-decoration:none;
	}
	
a.button4:hover{
	text-decoration:none;
	background:#187cc2;
	border:1px solid #187cc2;
	color:#fff;
	}
	
	
.ano_feature_section{
	float:left;
	width:100%;
	background:#fff;
	padding:80px 0 80px 0;
	border-bottom:1px solid #e7e7e7;
	}

img.imagethumb2{
	margin:100px 0 0 10px;
	}
	
	
.ano_feature_section .leftside{
	float:left;
	width:587px;
	margin:0 0 0 35px;
	}
    .arrow {
        position: relative;
        padding-left: 20px; /* Adjust space for the arrow */
    }
    
    .arrow::before {
        content: "\f105"; /* Font Awesome arrow */
        font-family: "Font Awesome 5 Free"; /* Ensure this matches the version you're using */
        font-weight: 900; /* Adjust weight based on the icon style */
        position: absolute;
        left: 0;
        top: 0; /* Align the icon vertically */
        color: rgb(173, 169, 169); /* Change to your desired color */
    }

.ano_feature_section .rightside{
	float:right;
	width:390px;
	margin:0 35px 0 0;
	}
	
/*** Footer ***/
.col-md-4:nth-child(even) {
  /*background: blue;*/
}
.col-md-4:nth-child(odd) {
 /*background: red;*/
}


::selection {
  background: #86857f;
  text-shadow: none;
}

footer {
  padding: 0.3rem 0 0.12rem 0 !important; /* Increased by 20% from minimal padding */
 }

.footer-column:not(:first-child) {
  padding-top: 0.3rem !important; /* Increased by 20% from minimal padding */
}

footer.bg-danger {
  padding: 0.3rem 0 0.12rem 0 !important; /* Match increased padding for colored footer */
}

section#footer footer {
  padding: 0.3rem 0 0.12rem 0 !important; /* Ensure all footers have increased padding */
}

#footer .footer-column {
  padding: 0.12rem !important; /* Increased by 20% from minimal column padding */
}

/* Remove margin-top from rounded footer if present */
footer.bg-danger, section#footer footer {
  margin-top: 0 !important;
}

/* Remove extra space below copyright */
.copyright {
  margin-bottom: 0 !important;
  padding-bottom: 0.2rem !important;
}

.fa-ellipsis-h {
  padding: 0.5rem 0 !important;
}
.banner {
    background-image: url('your-image-url.jpg'); /* Add your background image */
    background-size: cover; 
    color: #fff; 
    height: 200px; /* Set a specific height */
    width: 100%;
    display: flex;
    align-items: center; /* Center content vertically */
    justify-content: center; /* Center content horizontally */
}

.bannertext { 
    width: 100%;  
    border: 0.1em solid var(--yellow); 
    padding: 0.5em 0;
    text-align: center;
    
}

.bannertext p {
    padding: 0;
    text-transform: uppercase;
    color: var(--yellow);
    font-size: 1.5em; /* Adjust font size for better visibility */
}

@media (max-width: 600px) {
    .banner {
        height: 150px; /* Adjust height for smaller screens */
    }

    .bannertext p {
        font-size: 1.2em; /* Smaller font size */
    }
}
    section {
        max-width: 1500px;
        height: auto;
        margin: 0 auto;
        text-align: center;
        padding: 30px;
      }
    .section-lead {
        max-width: 600px;
        margin: 1rem auto 1.5rem;
      }
      
      .service a {
        color: var(--yellow);
        display: block;
      }
      .service p{
        color: #fff;
      }
      
      .service h4 {
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        color: var(--yellow);
        font-size: 1.3rem;
        margin: 1rem 0 0.6rem;
      }        .services-grid {
        /* Let Bootstrap handle the grid layout */
        margin: 0 -15px;
      }
        .service {
        background: transparent;
        /* Remove width: 50% to let Bootstrap handle responsive sizing */
        min-height: 350px;
        height: auto;
        margin: 15px 0;
        padding: 25px 20px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        /* Remove flex-wrap to prevent layout issues */
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        transition: all 0.3s ease;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.08);
      }
        .service:hover {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
      }      .img-small {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin: 0 auto 20px auto; /* Add bottom margin to separate from text */
        flex-shrink: 0; /* Prevent image from shrinking */
    }
      .service i {
        font-size: 3.45rem;
        margin: 0 0 20px 0; /* Add bottom margin */
        flex-shrink: 0;
      }
      
      .service h4 {
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        color: var(--yellow);
        font-size: 1.3rem;
        margin: 0 0 15px 0; /* Consistent spacing */
        flex-shrink: 0;
      }
      
      .service p {
        color: #fff;
        margin: 0;
        display: block;
        align-items: unset;
        flex-grow: unset;
        overflow-wrap: break-word;
        word-break: break-word;
      }
        .service1 i,
      .service2 i,
      .service3 i,
      .service4 i {
        color: white !important;
      }
      
      .service1 h4,
      .service1 .cta {
        color: var(--yellow);
      }
      
      .service1:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5); /* Increased shadow effect */
        transition: box-shadow 0.3s ease; /* Smooth transition */
      }
      
      .service2 h4,
      .service2 .cta {
        color: var(--yellow);
      }
      
      .service2:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5); /* Increased shadow effect */
        transition: box-shadow 0.3s ease; /* Smooth transition */
      }
      
      .service3 h4,
      .service3 .cta {
        color: var(--yellow);
      }
      
      .service3:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5); /* Increased shadow effect */
        transition: box-shadow 0.3s ease; /* Smooth transition */
      }
      
      .service4 h4,
      .service4 .cta {
        color: var(--yellow);
      }
      
      .service4:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5); /* Increased shadow effect */
        transition: box-shadow 0.3s ease; /* Smooth transition */
      }
      
      .service .cta span {
        font-size: 0.6rem;
      }
      
      .service > * {
        flex: unset !important;
      }
      .service p {
        color: #fff;
        margin: 0;
        display: block;
        align-items: unset;
        flex-grow: unset;
        overflow-wrap: break-word;
        word-break: break-word;
      }
        @media all and (max-width:900px) {
        .service {
          margin: 20px 0;
          padding: 20px 15px;
          min-height: 320px;
        }
        
        .img-small {
          margin-bottom: 15px;
        }
        
        .service h4 {
          margin-bottom: 12px;
          font-size: 1.2rem;
        }
      }
      .image-container {
        position: relative;
        width: 100%;
        height: 100%; /* Adjust height as needed */
        overflow: hidden; /* Ensures no overflow */
    }
    
    .about-img {
        position: absolute;
        top: 50%;
        left: 50%;
        max-width: 100%; /* Ensures the image is responsive */
        height: auto; /* Maintain aspect ratio */
        transform: translate(-50%, -50%);
        object-fit: cover; /* Cover the container */
    }

    /* services section start */


.services_section {
    width: 100%;
    float: left;
    background-image: url(../images/about-bg.png);
    height: auto;
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 10px 0;

}

.services_taital {
    width: 100%;
    font-size: 30px;
    color: #252525;
    text-align: center;
    font-weight: bold;
}
.services_taital::after {
    content: '';
    position: absolute;
    width: 85px;
    height: 10px;
    background-color: #000;
    left: 0px;
    right: 0px;
    margin: 0 auto;
    bottom: 0px;
    border-radius: 100px;
}

.services_text {
    width: 100%;
    font-size: 16px;
    color: #000;
    text-align: center;
    margin: 0px;
    padding-top: 30px;
}
.services_box {
    text-align: center;
    margin-bottom: 30px;
}
.express_text {
    width: 100%;
    float: left;
    font-size: 26px;
    color: #252525;
    text-align: center;
    padding-top: 68px;
}
.lorem_text{
    width: 90%;
    margin: 0 auto;
    color: #5e5a5a;
    text-align: center;
}
.seemore_bt{
    width: 170px;
    margin: 0 auto;
}
.seemore_bt a {
    width: 100%;
    float: left;
    font-size: 18px;
    color: #000;
    text-align: center;
    padding-top: 20px;
}
.seemore_bt a:hover{
    color: #0c426e;
}
.image_1 {
    max-width: 100%;
    height: auto;
    margin-top: 15px;
}
.padding_right_0{
    padding-right: 0px;
}
.padding_left_0{
    padding-left: 0px;
}

/* services section end */

.bg-contact{
    background: linear-gradient(to right, #1e73b2, #273675);;
}
/*** Service ***/
.service-item-top img {
    transition: .5s;
}

.service-item-top:hover img {
    transform: scale(1.1);
}

.service-carousel .owl-dots {
    margin-top: 24px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.service-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 15px;
    height: 15px;
    background: var(--primary);
    border: 5px solid var(--light);
    transition: .5s;
}

.service-carousel .owl-dot.active {
    background: var(--light);
    border-color: var(--primary);
}


.cards {
  display: flex;
  padding: 1rem;
  margin-bottom: 2rem;
  width: 100%;
  @media(min-width: 40rem) {
    width: 50%;
  }
  @media(min-width: 56rem) {
    width: 33.3%;
  }

  .card-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    width: 100%;
    border-radius: 6px;
    box-shadow: 0 20px 40px -14px rgba(0,0,0,0.25);
    overflow: hidden;
    transition: transform 0.5s;
    -webkit-transition: transform 0.5s;

    &:hover {
      cursor: pointer;
      transform: scale(1.1);
      -webkit-transform: scale(1.1);
      .card-image {
        opacity: 1;
      }
    }
  }

  
  .card-info {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    padding: 1rem;
    line-height: 1.5em;
  }

  .card-title {
    font-size: 25px;
    line-height: 1.1em;
    color: #32325d;
    margin-bottom: 0.2em;
  }

  
  .card-image{
    height: 200px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 6px 6px 0px 0px;
    opacity: 0.91;
  }
}
.wrapper {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.panel {
    margin-bottom: 15px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
}

.panel-heading {
    background-color: var(--primary);
    color: white;
    padding: 10px 15px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.panel-title a {
    color: white;
    text-decoration: none;
    display: block;
}

.panel-body {
    padding: 15px;
    background-color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wrapper {
        padding: 15px;
    }

    .panel-heading {
        text-align: center;
    }

    .panel-body {
        padding: 10px;
    }
}

.page-wrapper .banner-spot {
    padding: 0.4rem;
    background-image: none;
}
.blog-list .blog-meta.big-meta h4 {
    margin-left: -20%;
    margin-top: 1rem;

    padding: 1rem 1.5rem 1rem;
}
.post-media:hover .menucat {
    display: none;
}

.post-media img {
    width: 50%;
    background: linear-gradient(to right, #111518, #2b2b2c);;
    background-image: url('../img/ccnp.png'); /* Replace with your image URL */
    background-size: cover;
    background-position: center;
}
.blog-list .blog-meta.big-meta h4 {
    margin-left: -15%;
    margin-top: 1rem;
    
    padding: 1rem 1.5rem 1rem;
}
.blog-meta.big-meta h4 {
    padding: 1rem 0 0.6rem;
    margin: 0;
    font-size: 28px;
}
hr {
    border: 1px solid #ddd; /* Change the color as needed */
    margin: 20px 0; /* Space above and below the line */
    width: 80%;
  }
.blog-meta.big-meta p {
    margin-bottom: 0.5rem;
    padding-bottom: 0;
}

.blog-meta small {
    font-size: 11px;
    display: inline-block;
    margin-bottom: 0;
    padding-bottom: 0;
    color: #111111;
    font-weight: bold;
    text-transform: uppercase;
    margin-right: 0.5rem;
}

.blog-meta small:after {
    content: "/";
    padding-left: 1rem;
}

.blog-meta small:last-child:after {
    content: ""
}
@media (max-width: 768px) {
    .blog-box {
      flex-direction: column;
    }
    .blog-meta {
      text-align: center;
    }
  }
  
  .portifolio {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* Adjust the number of lines to show */
}
.services-bar{
    padding: 20px 0px 30px 0px;
    background: url(../img/dott.jpg);
    }
    .services-bar h1{
    font-weight: 600;
    font-size: 45px;
    text-align: center;
    margin-bottom: 0px;
    }
    .services-bar .card{
    box-shadow: 3px 5px 15px rgba(0,0,0, .15);
    border: none;
    }
    .services-bar .card h4.card-header{
    background-color: transparent;
    color: #1273eb;
    font-size: 18px;
    font-weight: 500;
    border: none;
    padding: 0px;
    margin-bottom: 15px;
    }
    .services-bar .card .card-footer{
    background-color: #001f54;
    }
    .services_section_2 {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .service-box {
        flex: 1 1 calc(33.33% - 20px); /* Adjusts width based on screen size */
        margin: 10px; /* Adds space between boxes */
        box-sizing: border-box;
    }

    .services_box {
        padding: 15px;
        border: 1px solid #eaeaea; /* Optional: add border for distinction */
        border-radius: 8px; /* Optional: rounded corners */
        background-color: #fff; /* Optional: background color */
    }

    .image_1 {
        max-width: 100%; /* Ensures images are responsive */
        height: auto; /* Maintains aspect ratio */
    }

    @media (max-width: 768px) {
        .service-box {
            flex: 1 1 100%; /* Stacks boxes on small screens */
        }
    }
    ul.nostyle {
        list-style: none;
        padding: 0;
        margin-bottom: 20px;
      }
      ul.nostyle i {
        color: #45aed6;
      }
      /* From Uiverse.io by cssbuttons-io */ 
.button {
    display: inline-block;
    padding: 12px 24px;
    border: 1px solid #4f4f4f;
    border-radius: 4px;
    transition: all 0.2s ease-in;
    background-color: var(--primary);
    position: relative;
    overflow: hidden;
    font-size: 19px;
    cursor: pointer;
    color: white;
    z-index: 1;
  }
  
  .button:before {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%) scaleY(1) scaleX(1.25);
    top: 100%;
    width: 140%;
    height: 180%;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    display: block;
    transition: all 0.5s 0.1s cubic-bezier(0.55, 0, 0.1, 1);
    z-index: -1;
  }
  
  .button:after {
    content: "";
    position: absolute;
    left: 55%;
    transform: translateX(-50%) scaleY(1) scaleX(1.45);
    top: 180%;
    width: 160%;
    height: 190%;
    background-color: var(--light);
    border-radius: 50%;
    display: block;
    transition: all 0.5s 0.1s cubic-bezier(0.55, 0, 0.1, 1);
    z-index: -1;
  }
  
  .button:hover {
    color: #000;
    border: 1px solid var(--light);
  }
  
  .button:hover:before {
    top: -35%;
    background-color: var(--secondary);
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
  
  .button:hover:after {
    top: -45%;
    background-color: var(--yellow);
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
 
  .button1 {
    display: inline-block;
    padding: 12px 24px;
    border: 1px solid var(--yellow);
    border-radius: 4px;
    transition: all 0.2s ease-in;
    background-color: var(--yellow);
    position: relative;
    overflow: hidden;
    font-size: 19px;
    cursor: pointer;
    color: #000;
    z-index: 1;
  }
  
  .button1:before {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%) scaleY(1) scaleX(1.25);
    top: 100%;
    width: 140%;
    height: 180%;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    display: block;
    transition: all 0.5s 0.1s cubic-bezier(0.55, 0, 0.1, 1);
    z-index: -1;
  }
  
  .button1:after {
    content: "";
    position: absolute;
    left: 55%;
    transform: translateX(-50%) scaleY(1) scaleX(1.45);
    top: 180%;
    width: 160%;
    height: 190%;
    background-color: var(--light);
    border-radius: 50%;
    display: block;
    transition: all 0.5s 0.1s cubic-bezier(0.55, 0, 0.1, 1);
    z-index: -1;
  }
  
  .button1:hover {
    color: #fff;
    border: 1px solid var(--secondary);
  }
  
  .button1:hover:before {
    top: -35%;
    background-color: var(--secondary);
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
  
  .button1:hover:after {
    top: -45%;
    background-color: var(--secondary);
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
 
  /* From Uiverse.io by cssbuttons-io */ 
  .button-outline {
    display: inline-block;
    padding: 12px 24px;
    border: 2px solid var(--light); /* Outline color */
    border-radius: 4px;
    transition: all 0.2s ease-in;
    position: relative;
    overflow: hidden;
    font-size: 19px;
    cursor: pointer;
    color: var(--yellow); /* Text color matches outline */
    background-color: transparent; /* Transparent background */
    z-index: 1;
  }
  .button-outline2 {
    display: inline-block;
    padding: 12px 24px;
    border: 2px solid var(--primary); /* Outline color */
    border-radius: 4px;
    transition: all 0.2s ease-in;
    position: relative;
    overflow: hidden;
    font-size: 19px;
    cursor: pointer;
    color: var(--light); /* Text color matches outline */
    background-color: var(--primary); /* Transparent background */
    z-index: 1;
  }
  
  .button-outline:before,
  .button-outline:after {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%) scaleY(1) scaleX(1.25);
    top: 100%;
    width: 140%;
    height: 180%;
    border-radius: 50%;
    display: block;
    transition: all 0.5s 0.1s cubic-bezier(0.55, 0, 0.1, 1);
    z-index: -1;
  }
  
  .button-outline:before {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .button-outline:after {
    left: 55%;
    transform: translateX(-50%) scaleY(1) scaleX(1.45);
    top: 180%;
    width: 160%;
    height: 190%;
    background-color: var(--yellow); /* Background color for effect */
  }
  
  .button-outline:hover {
    color: #ffff; /* Text color on hover */
    border: 2px solid var(--light); /* Change border color on hover */
  }
  
  .button-outline:hover:before {
    top: -35%;
    background-color: var(--yellow); /* Background color on hover */
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
  
  .button-outline:hover:after {
    top: -45%;
    background-color: var(--yellow); /* Background color on hover */
    transform: translateX(-50%) scaleY(1.3) scaleX(0.8);
  }
  @media (max-width: 767px) {
    .blog-box {
        flex-direction: column;
    }
    .post-media {
        margin-bottom: 20px;
    }
    .blog-meta {
        text-align: center;
    }

}
  
/* Service Section Additional Fixes */
#service {
    padding: 40px 0 80px 0; /* Reduced top padding from 80px to 40px */
    min-height: auto;
}

#service .services-grid {
    margin-top: 40px;
}

/* Responsive fixes for services */
@media (max-width: 768px) {
    .service {
        width: 100%;
        min-height: 280px;
        margin: 10px 0;
    }
    
    .services-grid {
        flex-direction: column;
        gap: 15px;
    }
    
    #service {
        padding: 60px 0;
    }
}

@media (max-width: 576px) {
    .service {
        min-height: 250px;
        padding: 15px;
    }
    
    .service h4 {
        font-size: 1.1rem;
    }
    
    .service p {
        font-size: 14px;
        line-height: 1.4;
    }
}

/* Footer logo styling - consolidated and fixed for new logo */
#footer .col-lg-3 img[src="img/logg.png"],
#footer .col-lg-3 img[src="img/CYVIRNET Logo-06.png"],
#footer footer .img-fluid,
footer .img-fluid {
    filter: brightness(0) invert(1) !important; /* Makes the logo white */
    transform: scale(1.2); /* Makes it 20% bigger */
    transition: transform 0.3s ease; /* Smooth transition for any hover effects */
}

.team-wrap .responsive-img,
.team-wrap .responsive-img:hover {
    filter: none !important;
}

.cert-logo img,
.cert-img,
.team-img img,
.responsive-img {
    filter: none !important;
    opacity: 1 !important;
}

/* Glass morph backgrounds for supporting logos: make white and square */
.cert-logo {
  background: #fff !important;
  border-radius: 0 !important;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06) !important;
  border: 1px solid #e0e0e0 !important;
  width: 90px !important;
  height: 90px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 10px auto !important;
}

.cert-img {
  max-width: 60px !important;
  max-height: 60px !important;
  object-fit: contain !important;
}

/* Remove any glassmorphism/blur from cert-logo */
.cert-logo {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Make supporting logo backgrounds rounded (circular) on About Us page */
.cert-logo {
  border-radius: 50% !important;
}

/* Make team page image height match the text paragraph to eliminate empty space */
.team-img {
  display: flex;
  align-items: stretch;
  height: 100%;
}

.team-member {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.team-img img.responsive-img {
  height: 100%;
  width: auto;
  object-fit: cover;
  max-height: none;
  min-height: 120px;
}

.team-wrap {
  height: 100%;
}

/* Ensure parent columns stretch to fit content */
.row.g-3, .row.g-4, .row.g-5 {
  align-items: stretch !important;
}

/* === FOOTER WHITE COLOR STYLING === */
/* Make all footer text white */
#footer,
#footer *,
section#footer,
section#footer *,
footer,
footer * {
  color: white !important;
}

/* Footer titles and headings - specifically Contact & Support */
#footer .footer-title,
.footer-title,
#footer h1,
#footer h2,
#footer h3,
#footer h4,
#footer h5,
#footer h6 {
  color: white !important;
  font-weight: bold !important;
}

/* Footer navigation links */
#footer .nav-link,
#footer .nav-link:hover,
#footer .nav-link:focus,
#footer .nav-link:active {
  color: white !important;
}

/* Footer list items */
#footer .nav-item,
#footer .nav-item span,
#footer ul,
#footer li {
  color: white !important;
}

/* Footer icons */
#footer .fas,
#footer .fa,
#footer .bi,
#footer i {
  color: white !important;
}

/* Footer paragraphs and text */
#footer p,
#footer span,
#footer div {
  color: white !important;
}

/* Footer logo - make it white */
#footer img,
#footer .img-fluid,
footer img,
footer .img-fluid {
  filter: brightness(0) invert(1) !important;
}

/* Change footer background to dark blue/navy */
footer.bg-danger,
section#footer footer.bg-danger {
  background-color: #1a237e !important; /* Dark blue instead of red */
}

/* === ROUNDED DESIGN FOR HEADER AND FOOTER === */

/* Header/Navbar Design - No Rounded Corners */
.navbar {
  border-radius: 0 !important;
  margin: 0 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: visible !important;
}

/* Navbar brand area - No rounded corners */
.navbar-brand {
  border-radius: 0 !important;
  padding: 8px 15px !important;
  margin: 5px !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

.navbar-brand:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: none !important;
}

/* Navigation items - No rounded corners */
.navbar-nav .nav-link {
  border-radius: 0 !important;
  margin: 0 5px !important;
  padding: 8px 15px !important;
  transition: all 0.3s ease !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  background: rgba(26, 35, 126, 0.1) !important;
  transform: none !important;
}

/* Dropdown menu - No rounded corners */
.dropdown-menu {
  border-radius: 0 !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  overflow: visible !important;
}

.dropdown-item {
  border-radius: 0 !important;
  margin: 0 !important;
  transition: all 0.3s ease !important;
}

.dropdown-item:hover {
  background: rgba(26, 35, 126, 0.1) !important;
  transform: none !important;
}

/* Rounded Footer Design */
section#footer {
  border-radius: 25px 25px 0 0 !important;
  margin: 0 15px !important;
  overflow: hidden !important;
  box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.1) !important;
}

footer.bg-danger {
  border-radius: 25px 25px 0 0 !important;
}

/* Footer content areas - no background, increased spacing by 20% */
#footer .footer-column {
  border-radius: 15px !important;
  padding: 6px !important;
  margin: 2.4px !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

#footer .footer-column:hover {
  background: transparent !important;
  transform: none !important;
}

/* Footer navigation items - no background, increased spacing by 20% */
#footer .nav-item {
  border-radius: 10px !important;
  margin: 1.2px 0 !important;
  padding: 2.4px 6px !important;
  transition: all 0.3s ease !important;
  background: transparent !important;
}

#footer .nav-item:hover {
  background: transparent !important;
}

/* Footer logo container - no background, increased spacing by 20% */
#footer .col-lg-3 {
  border-radius: 15px !important;
  padding: 2.4px !important;
  margin: 2.4px auto !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

#footer .col-lg-3:hover {
  background: transparent !important;
  transform: none !important;
}

/* Additional footer spacing - increased by 20% */
#footer .container {
  padding-top: 0.12rem !important;
  padding-bottom: 0.12rem !important;
}

#footer .row {
  margin-bottom: 0.12rem !important;
}

#footer .row:last-child {
  margin-bottom: 0 !important;
}

/* Override Bootstrap spacing classes in footer - increased by 20% */
#footer .mt-3,
#footer .mt-1 {
  margin-top: 0.12rem !important;
}

#footer .mb-3 {
  margin-bottom: 0.12rem !important;
}

/* Footer text line height - slightly increased */
#footer .nav-link {
  line-height: 1.32 !important;
  padding: 0.06rem 0 !important;
}

#footer .footer-title {
  line-height: 1.32 !important;
  margin-bottom: 0.12rem !important;
}

/* Footer logo size - increased by 20% */
#footer img {
  max-height: 42px !important;
  margin: 0.06rem 0 !important;
}

/* Contact button in navbar - No rounded corners */
.navbar .button {
  border-radius: 0 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.navbar .button:hover {
  transform: none !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Mobile responsive adjustments - No rounded navbar */
@media (max-width: 991.98px) {
  .navbar {
    margin: 0 !important;
    border-radius: 0 !important;
  }

  section#footer {
    margin: 0 10px !important;
    border-radius: 20px 20px 0 0 !important;
  }

  #footer .footer-column {
    margin: 2px !important;
    padding: 10px !important;
    border-radius: 10px !important;
    background: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .navbar {
    margin: 0 !important;
    border-radius: 0 !important;
  }

  section#footer {
    margin: 0 5px !important;
    border-radius: 15px 15px 0 0 !important;
  }

  .navbar-brand {
    border-radius: 0 !important;
    margin: 2px !important;
    padding: 5px 10px !important;
  }

  .navbar-nav .nav-link {
    border-radius: 0 !important;
    margin: 2px !important;
    padding: 6px 12px !important;
  }
}

@media (max-width: 575.98px) {
  .navbar {
    margin: 0 !important;
    border-radius: 0 !important;
  }

  section#footer {
    margin: 0 !important;
    border-radius: 10px 10px 0 0 !important;
  }
}
