(function ($) {
    "use strict";

    // Spinner
    var spinner = function () {
        setTimeout(function () {
            if ($('#spinner').length > 0) {
                $('#spinner').removeClass('show');
            }
        }, 1);
    };
    spinner();
    
    
    // Initiate the wowjs
    new WOW().init();


    // Smooth scrolling for navbar links
    $('.navbar-nav a[href^="#"]').on('click', function(event) {
        event.preventDefault();
        var target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 70 // Adjust for navbar height
            }, 800, 'easeInOutExpo');
        }
    });
    
    // Back to top button
    $(window).scroll(function () {
        if ($(this).scrollTop() > 300) {
            $('.back-to-top').fadeIn('slow');
        } else {
            $('.back-to-top').fadeOut('slow');
        }
    });
    $('.back-to-top').click(function () {
        $('html, body').scrollTop(0); // Instant scroll to top
        return false;
    });

    // Header carousel
    $(".header-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1500,
        items: 1,
        dots: true,
        loop: true,
        nav : true,
        navText : [
            '<i class="bi bi-chevron-left"></i>',
            '<i class="bi bi-chevron-right"></i>'
        ]
    });


    //**  Testimonials carousel
   

    $('.testimonial-carousel').owlCarousel({
        autoplay: true,
        smartSpeed: 1000,
        loop: true,
        margin: 10,
        nav: true,
        dots: false,
        loop: true,
        nav : false,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3 // Keep 2 items on larger screens
            }
        }
    });
    
    // Scroll down arrow functionality - instant scroll
    $('.scroll-arrow').on('click', function() {
        var footerSection = $('#footer');
        if (footerSection.length) {
            $('html, body').scrollTop(footerSection.offset().top); // Instant scroll to footer
        }
    });

    // Contact form submission (contact.html) - Using mailto
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = $('#submitBtn');
        var messageDiv = $('#formMessage');

        // Get form data
        var name = $('input[name="name"]', form).val().trim();
        var email = $('input[name="email"]', form).val().trim();
        var subject = $('input[name="subject"]', form).val().trim();
        var message = $('textarea[name="message"]', form).val().trim();

        // Validate form data
        if (!name || !email || !subject || !message) {
            messageDiv.removeClass('alert-success').addClass('alert alert-danger');
            messageDiv.text('Please fill in all fields before sending.').show();
            return;
        }

        // Validate email format
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            messageDiv.removeClass('alert-success').addClass('alert alert-danger');
            messageDiv.text('Please enter a valid email address.').show();
            return;
        }

        // Create email body
        var emailBody = "Name: " + name + "\n";
        emailBody += "Email: " + email + "\n";
        emailBody += "Subject: " + subject + "\n\n";
        emailBody += "Message:\n" + message + "\n\n";
        emailBody += "---\nThis message was sent from the CYVIRNET contact form.";

        // Create mailto link
        var mailtoLink = "mailto:<EMAIL>";
        mailtoLink += "?subject=" + encodeURIComponent("Contact Form: " + subject);
        mailtoLink += "&body=" + encodeURIComponent(emailBody);

        // Open email client
        window.location.href = mailtoLink;

        // Show success message
        messageDiv.removeClass('alert-danger').addClass('alert alert-success');
        messageDiv.html('✅ Your email client should now open with the message pre-filled.<br>Please send the email from your email client to complete the process.').show();

        // Clear form after a delay
        setTimeout(function() {
            form[0].reset();
        }, 1000);
    });

    // Home contact form submission (index.html) - Using mailto
    $('#homeContactForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var messageDiv = $('#homeFormMessage');

        // Get form data
        var name = $('input[name="name"]', form).val().trim();
        var email = $('input[name="email"]', form).val().trim();
        var subject = $('input[name="subject"]', form).val().trim();
        var message = $('textarea[name="message"]', form).val().trim();

        // Validate form data
        if (!name || !email || !subject || !message) {
            messageDiv.removeClass('alert-success').addClass('alert alert-danger');
            messageDiv.text('Please fill in all fields before sending.').show();
            return;
        }

        // Validate email format
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            messageDiv.removeClass('alert-success').addClass('alert alert-danger');
            messageDiv.text('Please enter a valid email address.').show();
            return;
        }

        // Create email body
        var emailBody = "Name: " + name + "\n";
        emailBody += "Email: " + email + "\n";
        emailBody += "Subject: " + subject + "\n\n";
        emailBody += "Message:\n" + message + "\n\n";
        emailBody += "---\nThis message was sent from the CYVIRNET contact form.";

        // Create mailto link
        var mailtoLink = "mailto:<EMAIL>";
        mailtoLink += "?subject=" + encodeURIComponent("Contact Form: " + subject);
        mailtoLink += "&body=" + encodeURIComponent(emailBody);

        // Open email client
        window.location.href = mailtoLink;

        // Show success message
        messageDiv.removeClass('alert-danger').addClass('alert alert-success');
        messageDiv.html('✅ Your email client should now open with the message pre-filled.<br>Please send the email from your email client to complete the process.').show();

        // Clear form after a delay
        setTimeout(function() {
            form[0].reset();
        }, 1000);
    });

})(jQuery);
