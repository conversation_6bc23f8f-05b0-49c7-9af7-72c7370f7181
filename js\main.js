(function ($) {
    "use strict";

    // Spinner
    var spinner = function () {
        setTimeout(function () {
            if ($('#spinner').length > 0) {
                $('#spinner').removeClass('show');
            }
        }, 1);
    };
    spinner();
    
    
    // Initiate the wowjs
    new WOW().init();


    // Smooth scrolling for navbar links
    $('.navbar-nav a[href^="#"]').on('click', function(event) {
        event.preventDefault();
        var target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 70 // Adjust for navbar height
            }, 800, 'easeInOutExpo');
        }
    });
    
    // Back to top button
    $(window).scroll(function () {
        if ($(this).scrollTop() > 300) {
            $('.back-to-top').fadeIn('slow');
        } else {
            $('.back-to-top').fadeOut('slow');
        }
    });
    $('.back-to-top').click(function () {
        $('html, body').scrollTop(0); // Instant scroll to top
        return false;
    });

    // Header carousel
    $(".header-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1500,
        items: 1,
        dots: true,
        loop: true,
        nav : true,
        navText : [
            '<i class="bi bi-chevron-left"></i>',
            '<i class="bi bi-chevron-right"></i>'
        ]
    });


    //**  Testimonials carousel
   

    $('.testimonial-carousel').owlCarousel({
        autoplay: true,
        smartSpeed: 1000,
        loop: true,
        margin: 10,
        nav: true,
        dots: false,
        loop: true,
        nav : false,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3 // Keep 2 items on larger screens
            }
        }
    });
    
    // Scroll down arrow functionality - instant scroll
    $('.scroll-arrow').on('click', function() {
        var footerSection = $('#footer');
        if (footerSection.length) {
            $('html, body').scrollTop(footerSection.offset().top); // Instant scroll to footer
        }
    });

    // Contact form submission (contact.html)
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = $('#submitBtn');
        var messageDiv = $('#formMessage');

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).text('Sending...');
        messageDiv.hide();

        // Submit form via AJAX
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    messageDiv.removeClass('alert-danger').addClass('alert alert-success');
                    messageDiv.text(response.message).show();
                    form[0].reset(); // Clear form
                } else {
                    messageDiv.removeClass('alert-success').addClass('alert alert-danger');
                    messageDiv.text(response.message).show();
                }
            },
            error: function() {
                messageDiv.removeClass('alert-success').addClass('alert alert-danger');
                messageDiv.text('Sorry, there was an error sending your message. Please try again later.').show();
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).text('Send Message');
            }
        });
    });

    // Home contact form submission (index.html)
    $('#homeContactForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = $('#homeSubmitBtn');
        var messageDiv = $('#homeFormMessage');

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).text('Sending...');
        messageDiv.hide();

        // Submit form via AJAX
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    messageDiv.removeClass('alert-danger').addClass('alert alert-success');
                    messageDiv.text(response.message).show();
                    form[0].reset(); // Clear form
                } else {
                    messageDiv.removeClass('alert-success').addClass('alert alert-danger');
                    messageDiv.text(response.message).show();
                }
            },
            error: function() {
                messageDiv.removeClass('alert-success').addClass('alert alert-danger');
                messageDiv.text('Sorry, there was an error sending your message. Please try again later.').show();
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).text('Send Message');
            }
        });
    });

})(jQuery);
