<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>CYVIRNET | Home</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://images.squarespace-cdn.com/content/v1/659c01d1b9d869462047df7b/6b205281-0425-4502-8b43-b4622a33598a/favicon.ico?format=100w">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
    .about-section {
	padding: 90px 0px;
	}
.service-section{
    padding: 40px 0px; /* Reduced from 90px to 40px */
}
.team-sections{
    padding: 70px 0px;
}
.why-sections{
    padding-top: 70px;
}
.testimonials-sections{
    padding-top: 70px;
}
.our-portfolio-sections{
    padding-top: 70px;
}
.col-lg-7 {
            text-align: center; /* Center text within the column */
        }                #footer .nav-link {
                    color: #d2c9c9; /* Change link color to white */
                    font-family:'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
                }

                /* Contact form message styling */
                #formMessage, #homeFormMessage {
                    padding: 15px;
                    border-radius: 8px;
                    font-weight: 500;
                    text-align: center;
                }

                .alert-success {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                }

                .alert-danger {
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                }

                /* Testimonial Carousel Fixes */
                #testimonialCarousel {
                    margin-bottom: 70px;
                }

                #testimonialCarousel .carousel-control-prev,
                #testimonialCarousel .carousel-control-next {
                    width: 50px;
                    height: 50px;
                    background: rgba(255, 255, 255, 0.15);
                    border-radius: 50%;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;
                }

                #testimonialCarousel .carousel-control-prev:hover,
                #testimonialCarousel .carousel-control-next:hover {
                    background: rgba(255, 255, 255, 0.25);
                    border-color: rgba(255, 255, 255, 0.5);
                    transform: translateY(-50%) scale(1.1);
                }

                #testimonialCarousel .carousel-control-prev {
                    left: -60px;
                }

                #testimonialCarousel .carousel-control-next {
                    right: -60px;
                }

                #testimonialCarousel .carousel-control-prev-icon,
                #testimonialCarousel .carousel-control-next-icon {
                    filter: invert(1);
                    width: 20px;
                    height: 20px;
                }

                #testimonialCarousel .carousel-indicators {
                    bottom: -60px;
                    margin-bottom: 0;
                }

                #testimonialCarousel .carousel-indicators [data-bs-target] {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    border: 2px solid rgba(255, 255, 255, 0.5);
                    background-color: transparent;
                    margin: 0 8px;
                    transition: all 0.3s ease;
                }

                #testimonialCarousel .carousel-indicators [data-bs-target].active {
                    background-color: #ffffff;
                    border-color: #ffffff;
                    transform: scale(1.2);
                }

                @media (max-width: 768px) {
                    #testimonialCarousel .carousel-control-prev {
                        left: -30px;
                    }
                    
                    #testimonialCarousel .carousel-control-next {
                        right: -30px;
                    }
                    
                    #testimonialCarousel .carousel-control-prev,
                    #testimonialCarousel .carousel-control-next {
                        width: 40px;
                        height: 40px;
                    }
                }                /* Why Choose Us Section - Consolidated Responsive Styles */                /* === WHY CHOOSE US: SCALING SOLUTION === */                #whychooseus {
                    padding: 60px 0;
                    overflow: hidden; /* Contain the scaled element and its layout box */
                    position: relative;
                    background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
                    transition: height 0.3s ease-in-out;
                    text-align: center; /* Ensure text content is centered */
                    min-height: 100vh; /* Force full viewport height */
                    display: flex;
                    align-items: center; /* Vertically center the content */
                }

                #whychooseus .container {
                    --mobile-scale: 1;
                    width: 1000px; /* Fixed desktop width */
                    position: relative; /* Enable centering with transforms */
                    left: 50%; /* Center horizontally */
                    transform: translateX(-50%) scale(var(--mobile-scale)); /* Center and scale */
                    transform-origin: top center; /* Scale from the top center */
                    transition: transform 0.3s ease-in-out;
                    padding: 0 !important; /* Override Bootstrap padding */
                    max-width: none !important; /* Override Bootstrap max-width */
                }

                #whychooseus .section-title {
                    text-align: center;
                    margin-bottom: 50px;
                }

                #whychooseus .section-title h1 {
                    font-size: 2.5rem;
                    color: #2a3a5e;
                    font-weight: 700;
                }                .why-chooseus-grid {
                    display: grid;
                    grid-template-columns: 220px 280px 220px;
                    grid-template-rows: 240px 240px;
                    gap: 40px 60px;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    z-index: 3;
                }.why-chooseus-grid .feature-card {
                    background: #fff;
                    border-radius: 15px;
                    box-shadow: 0 2px 12px rgba(0,0,0,0.04), 0 1.5px 8px rgba(80,120,255,0.07);
                    border: 1.5px solid #e3e9f7;
                    padding: 20px;
                    text-align: center;
                    width: 220px;
                    height: 220px; /* Increased height to fit content */
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    overflow: visible; /* Ensure content doesn't get cut off */
                }

                .why-chooseus-grid .feature-icon {
                    width: 60px;
                    height: 60px;
                    font-size: 24px;
                    background: linear-gradient(135deg, #4f8cff 60%, #7fd7e7 100%);
                    color: #fff;
                    border-radius: 50%;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                }

                .why-chooseus-grid .feature-card h4 {
                    font-size: 18px;
                    margin-bottom: 10px;
                    font-weight: 600;
                    color: #2a3a5e;
                }

                .why-chooseus-grid .feature-card p {
                    font-size: 14px;
                    line-height: 1.5;
                    margin-bottom: 0;
                    color: #4f5b7d;
                }

                .why-chooseus-grid .why-center-image {
                    grid-row: 1 / span 2;
                    grid-column: 2;
                    width: 280px;
                    height: 280px;
                }

                .why-chooseus-grid .why-center-image img.circle-img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 50%;
                    border: 4px solid #fff;
                }                /* Responsive Scaling with Vertical Centering */
                /* Remove fixed heights and maintain vertical centering on all screens */
                @media (max-width: 999px) {
                    #whychooseus { 
                        min-height: 100vh; /* Maintain full viewport height */
                        display: flex;
                        align-items: center; /* Keep vertical centering */
                    }
                    #whychooseus .container { 
                        --mobile-scale: 0.95; 
                    }
                }

                @media (max-width: 768px) {
                    #whychooseus { 
                        min-height: 100vh; /* Maintain full viewport height */
                        display: flex;
                        align-items: center; /* Keep vertical centering */
                    }
                    #whychooseus .container { 
                        --mobile-scale: 0.73; 
                    }
                }

                @media (max-width: 576px) {
                    #whychooseus { 
                        min-height: 100vh; /* Maintain full viewport height */
                        display: flex;
                        align-items: center; /* Keep vertical centering */
                    }
                    #whychooseus .container { 
                        --mobile-scale: 0.55; 
                    }
                }

                @media (max-width: 480px) {
                    #whychooseus { 
                        min-height: 100vh; /* Maintain full viewport height */
                        display: flex;
                        align-items: center; /* Keep vertical centering */
                    }
                    #whychooseus .container { 
                        --mobile-scale: 0.46; 
                    }
                }

                @media (max-width: 380px) {
                    #whychooseus { 
                        min-height: 100vh; /* Maintain full viewport height */
                        display: flex;
                        align-items: center; /* Keep vertical centering */
                    }
                    #whychooseus .container { 
                        --mobile-scale: 0.36; 
                    }
                }

    /* --- WHY CHOOSE US RESTORED & RESPONSIVE --- */
#whychooseus .section-title h1 {
    font-size: clamp(1.7rem, 4vw, 2.5rem);
}
/* Additional CSS for hover effects - works with our scaling solution */
.why-chooseus-grid .feature-card:hover {
    box-shadow: 0 8px 32px rgba(80,120,255,0.13), 0 2px 12px rgba(0,0,0,0.08);
    border-color: #4f8cff;
    transform: translateY(-6px) scale(1.03);
    background: linear-gradient(120deg, #f0f6ff 60%, #e3e9f7 100%);
}

/* Animations for hover effects and card animations */
@keyframes whyfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}

.why-chooseus-grid .feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #7fd7e7 0%, #4f8cff 100%);
    box-shadow: 0 4px 16px rgba(80,120,255,0.18);
}

/* Animations that still work with our scaling approach */
@keyframes whyfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}

@keyframes whyborderpulse {
    0%, 100% { opacity: 0.18; }
    50% { opacity: 0.32; }
}

.why-chooseus-grid .feature-card h4 {
    margin-bottom: clamp(0.5rem, 1.8vw, 0.7rem);
}

.why-chooseus-grid .feature-card p {
    font-size: clamp(0.85rem, 2.3vw, 1rem);
    line-height: 1.4;
}

@media (max-width: 575.98px) {
    /* PRESERVE 3-column layout with natural proportional scaling */
    .why-chooseus-grid {
        gap: clamp(0.5rem, 2.5vw, 1rem);
        max-width: 100%;
        width: 100%;
        padding: 0 1rem;
        /* Remove conflicting transform scale */
    }
    
    .why-chooseus-grid .feature-card {
        /* Use natural viewport-based scaling without fixed constraints */
        min-width: auto;
        max-width: none;
        width: 100%;
        padding: clamp(0.6rem, 2.5vw, 1rem);
        min-height: auto;
        /* Maintain aspect ratio naturally */
    }
    
    .why-chooseus-grid .feature-icon {
        width: clamp(28px, 6vw, 40px);
        height: clamp(28px, 6vw, 40px);
        font-size: clamp(0.8rem, 2vw, 1.1rem);
        margin-bottom: clamp(0.5rem, 2vw, 0.8rem);
    }
    
    .why-chooseus-grid .why-center-image {
        /* Natural scaling for center image */
        min-width: auto;
        min-height: auto;
        max-width: none;
        max-height: none;
        width: clamp(100px, 25vw, 160px);
        height: clamp(100px, 25vw, 160px);
    }
    
    /* Natural text scaling */
    .why-chooseus-grid .feature-card h4 {
        font-size: clamp(0.9rem, 3vw, 1.1rem);
        line-height: 1.3;
        margin-bottom: clamp(0.4rem, 1.5vw, 0.6rem);
    }
    
    .why-chooseus-grid .feature-card p {
        font-size: clamp(0.75rem, 2.2vw, 0.9rem);
        line-height: 1.4;
        margin-bottom: 0;
    }
}

/* Additional ultra-small screen optimization */
@media (max-width: 480px) {
    .why-chooseus-grid {
        gap: clamp(0.4rem, 2vw, 0.8rem);
        padding: 0 0.8rem;
    }
    
    .why-chooseus-grid .feature-card {
        padding: clamp(0.5rem, 2vw, 0.8rem);
    }
    
    .why-chooseus-grid .feature-icon {
        width: clamp(24px, 5vw, 36px);
        height: clamp(24px, 5vw, 36px);
        font-size: clamp(0.7rem, 1.8vw, 1rem);
    }
    
    .why-chooseus-grid .why-center-image {
        width: clamp(80px, 22vw, 140px);
        height: clamp(80px, 22vw, 140px);
    }
    
    .why-chooseus-grid .feature-card h4 {
        font-size: clamp(0.8rem, 2.8vw, 1rem);
    }
    
    .why-chooseus-grid .feature-card p {
        font-size: clamp(0.7rem, 2vw, 0.85rem);
    }
}

/* Landscape phone optimization */
@media (max-width: 667px) and (orientation: landscape) {
    .why-chooseus-grid {
        gap: clamp(0.6rem, 2vw, 1rem);
        max-width: 90%;
        margin: 0 auto;
    }
}

.why-chooseus-grid .why-center-image img.circle-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    background: #f5f5f5;
    box-shadow: 0 2px 16px rgba(0,0,0,0.07);
    aspect-ratio: 1/1;
    border: 4px solid #fff;
    position: relative;
    z-index: 1;
}

#whychooseus {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
    overflow: hidden;
}
#whychooseus .svg-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: 2;
    pointer-events: none;
}
.why-chooseus-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2.5rem 2.5rem;
    align-items: center;
    justify-items: center;
    position: relative;
    z-index: 3;
}
.why-chooseus-grid .feature-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.04), 0 1.5px 8px rgba(80,120,255,0.07);
    border: 1.5px solid #e3e9f7;
    padding: clamp(0.7rem, 2vw, 1.2rem) clamp(0.5rem, 2vw, 1rem);
    text-align: center;
    min-width: clamp(100px, 22vw, 180px);
    max-width: clamp(160px, 32vw, 260px);
    width: 100%;
    position: relative;
    overflow: hidden;
    animation: whyfadein 0.8s cubic-bezier(.4,0,.2,1) both;
}
.why-chooseus-grid .feature-card:hover {
    box-shadow: 0 8px 32px rgba(80,120,255,0.13), 0 2px 12px rgba(0,0,0,0.08);
    border-color: #4f8cff;
    transform: translateY(-6px) scale(1.03);
    background: linear-gradient(120deg, #f0f6ff 60%, #e3e9f7 100%);
}
.why-chooseus-grid .feature-icon {
    width: clamp(32px, 7vw, 60px);
    height: clamp(32px, 7vw, 60px);
    font-size: clamp(1rem, 2vw, 1.7rem);
    background: linear-gradient(135deg, #4f8cff 60%, #7fd7e7 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(80,120,255,0.10);
    border-radius: 50%;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.3s, background 0.3s, width 0.3s, height 0.3s, font-size 0.3s;
}
.why-chooseus-grid .feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #7fd7e7 0%, #4f8cff 100%);
    box-shadow: 0 4px 16px rgba(80,120,255,0.18);
}
.why-chooseus-grid .feature-card h4 {
    font-size: clamp(0.95rem, 2.5vw, 1.3rem);
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2a3a5e;
    letter-spacing: 0.01em;
    transition: font-size 0.3s;
}
.why-chooseus-grid .feature-card p {
    font-size: clamp(0.85rem, 2vw, 1.05rem);
    line-height: 1.5;
    margin-bottom: 0;
    color: #4f5b7d;
    transition: font-size 0.3s;
}
.why-chooseus-grid .why-center-image {
    grid-row: 1 / span 2;
    grid-column: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 180px;
    min-height: 180px;
    max-width: 320px;
    max-height: 320px;
    width: 100%;
    height: 100%;
    position: relative;
    animation: whyfadein 1.1s cubic-bezier(.4,0,.2,1) both;
    z-index: 1;
}
.why-chooseus-grid .why-center-image::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
    z-index: 0;
    opacity: 0.18;
    animation: whyborderpulse 2.5s infinite cubic-bezier(.4,0,.2,1);
}
.why-chooseus-grid .why-center-image img.circle-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    background: #f5f5f5;
    box-shadow: 0 2px 16px rgba(0,0,0,0.07);
    aspect-ratio: 1/1;
    border: 4px solid #fff;
    position: relative;
    z-index: 1;
}
@keyframes whyfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}
@keyframes whyborderpulse {
    0%, 100% { opacity: 0.18; }
    50% { opacity: 0.32; }
}

#about {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
    overflow: hidden;
    z-index: 1;
}
#about .svg-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: 2;
    pointer-events: none;
}
#about .about-card {
    background: rgba(255,255,255,0.97);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(80,120,255,0.07), 0 1.5px 8px rgba(80,120,255,0.09);
    padding: clamp(1.2rem, 3vw, 2.2rem) clamp(1rem, 3vw, 2rem);
    margin-bottom: 1.5rem;
    animation: aboutfadein 0.9s cubic-bezier(.4,0,.2,1) both;
    border: 1.5px solid #e3e9f7;
}
#about .about-card h5 {
    color: #4f8cff;
    font-weight: 700;
    letter-spacing: 0.04em;
    margin-bottom: 1rem;
}
#about .about-card p {
    color: #2a3a5e;
    font-size: clamp(1rem, 2vw, 1.13rem);
    line-height: 1.7;
    margin-bottom: 0.7rem;
}
#about .about-card .text-end a {
    color: #4f8cff;
    font-weight: 600;
    transition: color 0.2s;
}
#about .about-card .text-end a:hover {
    color: #2a3a5e;
    text-decoration: underline;
}
#about .about-img-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    animation: aboutfadein 1.1s cubic-bezier(.4,0,.2,1) both;
}
#about .about-img-wrap img {
    border-radius: 50%;
    box-shadow: 0 4px 24px rgba(80,120,255,0.13);
    border: 4px solid #fff;
    background: #f5f5f5;
    max-width: 420px;
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
    transition: box-shadow 0.3s, border 0.3s;
}
@media (max-width: 1199.98px) {
    #about .about-img-wrap img {
        max-width: 320px;
    }
}
@media (max-width: 991.98px) {
    #about .about-img-wrap img {
        max-width: 200px;
    }
}
@media (max-width: 767.98px) {
    #about .about-img-wrap img {
        max-width: 120px;
    }
    #about .about-card {
        padding: clamp(0.5rem, 2vw, 0.8rem) clamp(0.3rem, 2vw, 0.7rem);
    }
    #about .about-img-wrap {
        margin-bottom: 1.5rem;
    }
    #about .row.align-items-center {
        flex-direction: column;
    }
}
@keyframes aboutfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}
@keyframes aboutborderpulse {
    0%, 100% { opacity: 0.13; }
    50% { opacity: 0.22; }
}
.fadein-about {
    opacity: 0;
    animation: aboutfadein 1.1s cubic-bezier(.4,0,.2,1) both;
}

/* === ENHANCED HERO SECTION === */
.hero-section {
    position: relative;
    min-height: 45vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 40px 0 30px;
    background: linear-gradient(135deg, #1a237e 0%, #0d47a1 25%, #1565c0 50%, #1976d2 75%, #2196f3 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
    background-size: 100px 100px, 150px 150px, 200px 200px;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26,35,126,0.8) 0%, rgba(13,71,161,0.6) 50%, rgba(25,118,210,0.4) 100%);
    z-index: 2;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255,255,255,0.6);
    border-radius: 50%;
    animation: particleFloat 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: -1s;
    animation-duration: 8s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: -3s;
    animation-duration: 10s;
}

.particle:nth-child(3) {
    top: 40%;
    left: 60%;
    animation-delay: -2s;
    animation-duration: 7s;
}

.particle:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: -4s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    top: 30%;
    left: 90%;
    animation-delay: -1.5s;
    animation-duration: 6s;
}

.hero-content {
    position: relative;
    z-index: 10;
    animation: heroContentSlideUp 1.2s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 50px;
    padding: 8px 18px;
    font-size: 0.9rem;
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 1.8rem;
    animation: heroContentSlideUp 1.4s cubic-bezier(0.4, 0, 0.2, 1) both;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.hero-title {
    font-size: clamp(2.2rem, 5.5vw, 3.8rem);
    font-weight: 800;
    line-height: 1.1;
    color: #ffffff;
    margin-bottom: 1.4rem;
    text-shadow: 0 2px 20px rgba(0,0,0,0.3);
}

.hero-line-1,
.hero-line-2,
.hero-line-3 {
    display: block;
    opacity: 0;
    transform: translateY(50px);
    animation: heroLineSlideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.hero-line-1 {
    animation-delay: 0.2s;
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-line-2 {
    animation-delay: 0.4s;
    background: linear-gradient(135deg, #ffffff 0%, #bbdefb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-line-3 {
    animation-delay: 0.6s;
    background: linear-gradient(135deg, #ffffff 0%, #90caf9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: clamp(1.05rem, 2.3vw, 1.25rem);
    line-height: 1.6;
    color: rgba(255,255,255,0.9);
    margin-bottom: 2.2rem;
    max-width: 600px;
    animation: heroContentSlideUp 1.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.hero-buttons {
    display: flex;
    gap: 1.3rem;
    margin-bottom: 2.8rem;
    flex-wrap: wrap;
    animation: heroContentSlideUp 1.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.btn-hero-primary {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: #ffffff;
    padding: 14px 28px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.05rem;
    text-decoration: none;
    border: none;
    box-shadow: 0 8px 25px rgba(255,107,53,0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

.btn-hero-primary:hover {
    color: #ffffff;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255,107,53,0.4);
}

.btn-hero-secondary {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    color: #ffffff;
    padding: 14px 28px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.05rem;
    text-decoration: none;
    border: 2px solid rgba(255,255,255,0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-hero-secondary:hover {
    color: #1976d2;
    background: rgba(255,255,255,0.95);
    border-color: rgba(255,255,255,0.8);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255,255,255,0.2);
}

.hero-stats {
    display: flex;
    gap: 2.8rem;
    flex-wrap: wrap;
    animation: heroContentSlideUp 2s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.stat-number {
    font-size: clamp(1.6rem, 3.8vw, 2.3rem);
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
    margin-bottom: 0.4rem;
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.7);
    font-weight: 500;
}

.hero-visual {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 420px;
    animation: heroVisualSlideIn 1.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.hero-circle-1,
.hero-circle-2 {
    position: absolute;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.1);
}

.hero-circle-1 {
    width: 260px;
    height: 260px;
    animation: heroCircleRotate 20s linear infinite;
}

.hero-circle-2 {
    width: 340px;
    height: 340px;
    animation: heroCircleRotate 25s linear infinite reverse;
}

.hero-icon-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.8rem;
    z-index: 2;
}

.hero-icon {
    width: 75px;
    height: 75px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: heroIconFloat 3s ease-in-out infinite;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.hero-icon:nth-child(1) { animation-delay: -0.5s; }
.hero-icon:nth-child(2) { animation-delay: -1s; }
.hero-icon:nth-child(3) { animation-delay: -1.5s; }
.hero-icon:nth-child(4) { animation-delay: -2s; }

.hero-icon:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    animation: heroScrollIndicator 2s ease-in-out infinite;
}

.scroll-arrow {
    width: 38px;
    height: 38px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.scroll-arrow:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    transform: scale(1.1);
}

/* === ANIMATIONS === */
@keyframes heroBackgroundMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) translateX(0); opacity: 0.6; }
    25% { transform: translateY(-15px) translateX(8px); opacity: 1; }
    50% { transform: translateY(-8px) translateX(-4px); opacity: 0.8; }
    75% { transform: translateY(-12px) translateX(12px); opacity: 0.9; }
}

@keyframes heroContentSlideUp {
    0% { opacity: 0; transform: translateY(50px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes heroLineSlideUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes heroVisualSlideIn {
    0% { opacity: 0; transform: translateX(80px) scale(0.9); }
    100% { opacity: 1; transform: translateX(0) scale(1); }
}

@keyframes heroCircleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes heroIconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

@keyframes heroScrollIndicator {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(8px); }
}

/* === RESPONSIVE === */
@media (max-width: 1199.98px) {
    .hero-stats {
        gap: 2.2rem;
    }
    .hero-circle-1 {
        width: 220px;
        height: 220px;
    }
    .hero-circle-2 {
        width: 280px;
        height: 280px;
    }
    .hero-icon {
        width: 65px;
        height: 65px;
        font-size: 1.6rem;
    }
    .hero-icon-grid {
        gap: 2.3rem;
    }
}

@media (max-width: 991.98px) {
    .hero-visual {
        height: 350px;
    }
    .hero-stats {
        gap: 1.8rem;
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        min-height: 40vh;
        padding: 30px 0 20px;
    }
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    .hero-stats {
        gap: 1.2rem;
        justify-content: space-around;
    }
    .stat-item {
        flex: 1;
        min-width: 90px;
        padding: 12px 15px;
    }
}

@media (max-width: 575.98px) {
    .hero-section {
        min-height: 35vh;
        padding: 25px 0 15px;
    }
    .hero-badge {
        font-size: 0.8rem;
        padding: 6px 14px;
    }
    .hero-description {
        margin-bottom: 1.8rem;
    }
    .hero-buttons {
        margin-bottom: 2.2rem;
    }
    .hero-stats {
        gap: 1rem;
    }
    .stat-item {
        padding: 10px 12px;
    }
}
    </style>
    

</head>

<body class="home">
       <div class="p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <img src="img/CYVIRNET Logo-01.png" style="width: 100px; height: auto;" alt="Loading...">
        </div>
        </div>
        <!-- Spinner End -->

   <!-- Navbar Start -->
   <nav class="navbar navbar-expand-lg bg-white navbar-light shadow fixed-top w-100">    <a href="index.html" class="navbar-brand d-flex align-items-center text-center px-4 px-lg-5">
        <img src="img/Log.jpg" class="img-fluid" alt="Logo" style="width: 48%; height: auto;">
    </a>
    <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto p-4 p-lg-0">
            <a href="index.html" class="nav-item nav-link active">Home</a>
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">About</a>
                <div class="dropdown-menu fade-down m-0">
                    <a href="about.html" class="dropdown-item">About Us</a>
                    <a href="Our_Portifolio.html" class="dropdown-item">Our Portfolio</a>
                    <a href="testimonials.html" class="dropdown-item">Testimonial</a>
                </div>
            </div>
            <a href="service.html" class="nav-item nav-link">Services</a>
            <a href="team.html" class="nav-item nav-link">Our Team</a>
            <div class="pe-5 py-2">
                <a href="contact.html">
                    <button class="button rounded px-lg-5 d-md-block">Contact Us</button>
                </a>
            </div>        </div>
    </div>
   </nav>   <!-- Navbar End -->

        <!-- Enhanced Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-background">
                <div class="hero-overlay"></div>
                <div class="hero-particles">
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                </div>
            </div>
            <div class="container h-100">
                <div class="row h-100 align-items-center">
                    <div class="col-lg-7 col-md-8">
                        <div class="hero-content">
                            <div class="hero-badge">
                                <i class="fas fa-shield-alt me-2"></i>
                                <span>Trusted Cybersecurity Partner</span>
                            </div>                            <h1 class="hero-title" style="text-align: left; color: white;">
                                Technology Networking Cybersecurity & Cloud Solutions Provider
                            </h1>
                            <p class="hero-description" style="text-align: left; color: rgba(255,255,255,0.9);">
                                We offer a range of professional services that cater to your networking, cybersecurity and cloud needs
                            </p>
                            <div class="hero-buttons">
                                <a href="service.html" class="btn-hero-primary">
                                    <span>Explore Services</span>
                                    <i class="fas fa-arrow-right ms-2"></i>
                                </a>
                                <a href="contact.html" class="btn-hero-secondary">
                                    <i class="fas fa-phone me-2"></i>
                                    <span>Get Started</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5 col-md-4 d-none d-md-block">
                        <div class="hero-visual">
                            <div class="hero-circle-1"></div>
                            <div class="hero-circle-2"></div>
                            <div class="hero-icon-grid">
                                <div class="hero-icon"><i class="fas fa-shield-alt"></i></div>
                                <div class="hero-icon"><i class="fas fa-cloud"></i></div>
                                <div class="hero-icon"><i class="fas fa-network-wired"></i></div>
                                <div class="hero-icon"><i class="fas fa-lock"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hero-scroll-indicator">
                <div class="scroll-arrow">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </section>
        <!-- End Enhanced Hero Section -->
   </div>
    </section>
    <!-- End Spinner -->

   <!-- About Us Section -->
<section id="about" class="about-section-modern position-relative">
    <div class="about-svg-wave">
        <svg viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
            <path d="M0,30 C360,60 1080,0 1440,30 L1440,60 L0,60 Z" fill="#e3e9f7"/>
        </svg>
    </div>
    <div class="container py-5">
        <div class="row align-items-center justify-content-center g-5">
            <div class="col-lg-7 order-1 d-flex align-items-center">
                <div class="about-card fadein-about w-100">
                    <h5 class="fw-bold text-primary text-uppercase mb-3">About Us</h5>
                    <p class="text-black mb-3">
                        CYVIRNET is a leading cybersecurity, network, and cloud 
                        solutions provider committed to innovation and delivering world-class technology services.
                        We view technology not as a single practice, but as several interlocking disciplines, including consulting, 
                        installation and maintenance, security, scalability, and resilience.
                    </p>
                    <p class="text-black mb-3">
                        As partners, we strive for honesty and clarity. Our first job is to understand the client's 
                        vision and needs, rather than presenting our own. We value timeliness, direct communication, and practical 
                        solutions over lengthy presentations, complemented by the occasional face-to-face meeting.
                    </p>
                    <p class="text-black mb-3">
                        At the core of our company lies a strong sense of purpose and unwavering values, driving us to provide solutions
                        that not only meet technological needs but also align with principles of integrity, transparency, and client satisfaction.
                    </p>
                    <div class="text-end pt-2">
                        <a class="text-blue mon-font font-b" href="about.html">Read More <i class="fa fa-arrow-right ms-3"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 order-2 d-flex justify-content-center mb-4 mb-lg-0">
                <div class="about-img-wrap fadein-about">
                    <img src="img/img_9.jpg" class="about-circle-img" alt="A visual representation of cyber security" />
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End About Us Section --> <!-- Buttons Start -->
<section id="service" class="service-section" style="padding-bottom: 10px;">
    <div class="container-fluid">
        <div class="container">
            <h1 class="text-center text-white mb-2">Our Services</h1>
            <div class="row text-center">
                <div class="col-12 d-flex flex-column align-items-center">
                    <p class="text-yellow text-center mb-1 px-3">
                        We value timeliness, direct communication, and practical solutions over presentations.
                        This enables us to design custom, cutting-edge, cost-effective solutions and provide elite 
                        products and services to our clients.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Buttons End -->

<!-- Our Services -->
   <section id="service" style="padding-top: 10px;">
    <div class="container-fluid">
        <div class="container">
            <div class="row services-grid g-4">
                <article class="col-12 col-md-6 col-lg-3 service service1 d-flex flex-column align-items-center text-center" style="padding: 20px;">
                    <i class="fas fa-exclamation-circle service-icon"></i>
                    <h4>The Problem</h4>
                    <p>Companies faced with issues related to software development delays, system failures, and cybersecurity risks.</p>
                </article>

                <article class="col-12 col-md-6 col-lg-3 service service2 d-flex flex-column align-items-center text-center" style="padding: 20px;">
                    <i class="fas fa-lightbulb service-icon"></i>
                    <h4>The Solution</h4>
                    <p>We provide products or services designed to protect digital systems and data from cyberattacks.</p>
                </article>

                <article class="col-12 col-md-6 col-lg-3 service service3 d-flex flex-column align-items-center text-center" style="padding: 20px;">
                    <i class="fas fa-search service-icon"></i>
                    <h4>The Why</h4>
                    <p>The number of cybercrimes worldwide is increasing, and cybercriminals are becoming more sophisticated.</p>
                </article>

                <article class="col-12 col-md-6 col-lg-3 service service4 d-flex flex-column align-items-center text-center" style="padding: 20px;">
                    <i class="fas fa-briefcase service-icon"></i>
                    <h4>Opportunity</h4>
                    <p>Choose a strong cybersecurity policy that safeguards your organization's systems and data from cyberattacks.</p>                </article>
            </div>
            <div class="row g-4 text-center mt-4">
                <div class="col-12 d-flex flex-column align-items-center">
                    <a href="service.html">
                        <button class="button1 rounded px-lg-5 mb-2 d-md-block btn btn-sm w-100">Explore Services</button>
                    </a>
                </div>
            </div>    
        </div>
    </div>
   </section>
   <!-- End Our Services -->          <!-- Why Choose Us Section --> 
    <div id="whychooseus">
        <div class="container">
            <div class="section-title">
                <h1 class="fw-bold text-primary">Why Choose Us</h1>
            </div>
            <div class="why-chooseus-grid">
                <!-- Feature Card 1 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fa fa-thumbs-up"></i>
                    </div>
                    <h4>Comprehensive Expertise</h4>
                    <p>We boast a comprehensive suite of IT services covering networking, cybersecurity, cloud virtualization.</p>
                </div>
                <!-- Feature Card 2 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fa fa-lock"></i>
                    </div>
                    <h4>Proactive Cybersecurity Measures</h4>
                    <p>Security threats continue to evolve, making robust cybersecurity a necessity.</p>
                </div>
                <!-- Center Image -->
                <div class="why-center-image">
                    <img src="img/img3.jpg" alt="Why Choose Us" class="circle-img" />
                </div>
                <!-- Feature Card 3 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fa fa-users-cog"></i>
                    </div>
                    <h4>Tailored Solutions</h4>
                    <p>Our approach revolves around offering customized solutions tailored to each client's specific needs.</p>
                </div>
                <!-- Feature Card 4 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fa fa-cloud"></i>
                    </div>
                    <h4>Scalable Cloud Solutions</h4>
                    <p>Embracing cloud technology is essential for modern businesses.</p>
                </div>
            </div>
        </div>
    </div>    
<!-- End Why Us Section -->
              <!-- Customer Feedback Start -->
     <section id="testimonial" class="testimonial-section">
        <div class="container-fluid py-0">
            <div class="container">
                <h1 class="text-center text-white mb-5">Customer Feedback</h1>
                <div id="testimonialCarousel" class="carousel slide position-relative" data-bs-ride="carousel" data-bs-interval="5000">
                    <div class="carousel-inner">
                        <!-- First carousel item -->
                        <div class="carousel-item active">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="row">
                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">They consistently met deadlines and delivered quality work on time. Their commitment to the schedule made a significant difference in our overall timeline.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">John Batahana</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">The team was highly organized and kept us updated throughout the project. Their attention to detail was impressive, making the entire process seamless.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">Blessing Mashy</h5>
                                            </div>
                                        </div>                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">The professionalism displayed throughout the project was commendable. They approached challenges with a positive attitude and a solution-oriented mindset.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">Roy Mc'lister</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Second carousel item -->
                        <div class="carousel-item">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="row">
                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">Their innovative solutions helped us streamline our processes and improve efficiency significantly. The results exceeded our expectations.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">Sarah Johnson</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">Outstanding customer service and technical expertise. They were always available to answer questions and provide support when needed.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">Michael Chen</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-start rounded p-4">
                                            <i class="fa fa-quote-left fa-2x text-yellow mb-3"></i>
                                            <p class="text-white">Working with this team was a game-changer for our business. Their cybersecurity solutions gave us peace of mind and improved our overall security posture.</p>
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-1 text-yellow">Amanda Smith</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>                    <!-- Carousel controls with proper positioning -->
                    <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                    <!-- Carousel indicators -->
                    <div class="carousel-indicators">
                        <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                        <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                    </div>
                </div>
            </div>
        </div>
     </section>
    <!-- Testimonial End -->

    <!-- Portfolio Start -->
     <section id="portfolio" class="our-portfolio-sections">
        <div class="container-fluid py-0 category bg-white">
            <div class="container">
                <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
                    <h1 class="section-title bg-white text-center text-primary px-3 py-4">Our Portfolio</h1>
                </div>                <div class="row g-3">
                    <div class="col-lg-5 col-md-6 wow fadeIn order-2 order-md-1" data-wow-delay="0.5s">
                    <h6 class="text-secondary text-uppercase"></h6>
                    <h1 class="mb-4">Secure Wi-Fi Installation & Maintenance</h1>
                    <p class="mb-4 text-start">CYVIRNET is currently implementing and operating secure guest WiFi solutions at these major airports, ensuring seamless and secure connectivity for travelers while maintaining robust cybersecurity measures.</p>
                    <div class="row">
                        <h6 class="">We also have successful experience in the following industries</h6>
                        <div class="col-sm-6">
                            <ul class="nostyle">
                                <li><i class="fa fa-check-square"></i> Agriculture</li>
                                <li><i class="fa fa-check-square"></i> Banking & Finance Services</li>
                                <li><i class="fa fa-check-square"></i> Retail</li>
                            </ul>
                        </div>
                        <div class="col-sm-6">
                            <ul class="nostyle">
                                <li><i class="fa fa-check-square"></i> Education & Retail</li>
                                <li><i class="fa fa-check-square"></i> Transport</li>
                                <li><i class="fa fa-check-square"></i> Cybersecurity engagements</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 order-1 order-md-2">
                    <div class="row g-3">
                        <div class="col-lg-12 col-md-12 wow zoomIn" data-wow-delay="0.1s">
                            <a class="position-relative d-block overflow-hidden">
                                <img class="img-fluid" src="img/harare_airpot.jpg" alt="">                                <div class="bg-white text-center position-absolute top-50 start-50 translate-middle" style="opacity: 0.8; padding: 10px; border-radius: 15px;">
                                    <h5 class="m-0">Harare</h5>
                                    <small class="text-primary">International Airport</small>
                                </div>
                            </a>
                        </div>
                        <div class="col-lg-6 col-md-12 wow zoomIn" data-wow-delay="0.3s">
                            <a class="position-relative d-block overflow-hidden">
                                <img class="img-fluid" src="img/vicFall_airport.jpg" alt="">                                <div class="bg-white text-center position-absolute top-50 start-50 translate-middle" style="opacity: 0.8; padding: 10px; border-radius: 15px;">
                                    <h5 class="m-0">Victoria Falls</h5>
                                    <small class="text-primary">International Airport</small>
                                </div>
                            </a>
                        </div>
                        <div class="col-lg-6 col-md-12 wow zoomIn" data-wow-delay="0.5s">
                            <a class="position-relative d-block overflow-hidden">
                                <img class="img-fluid" src="img/68.jpg" alt="">                                <div class="bg-white text-center position-absolute top-50 start-50 translate-middle" style="opacity: 0.8; padding: 10px; border-radius: 15px;">
                                    <h5 class="m-0">Bulawayo</h5>
                                    <small class="text-primary">JMN International Airport</small>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="text-end p-5 w-100">
                    <a class="py-0 px-5 mt-3 text-blue mon-font font-b" href="Our_Portifolio.html">Read More <i class="fa fa-arrow-right ms-3"></i></a>
                </div>
            </div>
        </div>
     </section>
    <!-- Portfolio End -->


    <!-- Team Start -->
     <section id="team" class="team-sections">
        <div class="container-fluid py-0">
            <div class="container bg-white">
                <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
                    <h1 class="section-title bg-white text-center text-primary px-3 py-5">Our Team</h1>
                </div>
                <div class="row">
                    <div class="col-lg-5 col-md-6 wow fadeIn order-1 order-md-1" data-wow-delay="0.5s">
                        <h6 class="text-secondary text-uppercase"></h6>
                    <h1 class="mb-4">Our Team's Expertise,</h1>
                    <h5 class="">Expert Solutions, Tailored for You</h5>
                    <p class="py-4 ">We value timeliness, direct communication and practical solutions over products and services to our clients.</p>
                    
                    </div>
                      <div class="col-lg-6 mx-auto py-5 bg-white order-2 order-md-2">
                        <div class="row g-3">
                            <div class="col-6 col-md-3 team-wrap">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/aws.png" style="width: 90%; height: auto;" alt="AWS Certification">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 col-md-3 team-wrap">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/juniper.png" style="width: 90%; height: auto;" alt="Juniper Certification">
                                    </div>
                                </div>
                            </div>

                            <div class="col-6 col-md-3 team-wrap">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/ccna.png" style="width: 90%; height: auto;" alt="Cisco CCNA Certification">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 col-md-3 team-wrap">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/oscp.png" style="width: 90%; height: auto;" alt="OSCP Certification">
                                    </div>
                                </div>
                            </div>

                            <div class="col-6 col-md-3 team-wrap mt-3">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/ceh.png" style="width: 90%; height: auto;" alt="CEH Certification">
                                    </div>
                                </div>
                            </div>

                            <div class="col-6 col-md-3 team-wrap mt-3">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/cisa.png" style="width: 90%; height: auto;" alt="CISA Certification">
                                    </div>
                                </div>
                            </div>

                            <div class="col-6 col-md-3 team-wrap mt-3">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/giac.png" style="width: 90%; height: auto;" alt="GIAC Certification">
                                    </div>
                                </div>
                            </div>

                            <div class="col-6 col-md-3 team-wrap mt-3">
                                <div class="team-member text-center">
                                    <div class="team-img">
                                        <img class="responsive-img" src="img/brands/comp.png" style="width: 90%; height: auto;" alt="CompTIA Certification">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
     </section>
<!-- Team End -->

<!-- Contact Start -->
<section id="contact">
    <div class="container-fluid py-0">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-6 order-1 order-md-1">
                    <div class="wow fadeInUp" data-wow-delay="0.5s">
                        <p class="mb-4 text-white">We embrace new opportunities and are comfortable working internationally. Please get in touch and one of our project.</p>
                        <form id="homeContactForm" action="send_email.php" method="POST">
                            <div class="row g-3">
                                <div class="col-12 col-md-6">
                                    <input type="text" class="form-control border-0" name="name" placeholder="Your Name" style="height: 55px;" required>
                                </div>
                                <div class="col-12 col-md-6">
                                    <input type="email" class="form-control border-0" name="email" placeholder="Your Email" style="height: 55px;" required>
                                </div>
                                <div class="col-12">
                                    <input type="text" class="form-control border-0" name="subject" placeholder="Subject" style="height: 55px;" required>
                                </div>
                                <div class="col-12">
                                    <textarea class="form-control border-0" name="message" rows="6" placeholder="Message" required></textarea>
                                </div>
                                <div class="col-12">
                                    <button class="button1 w-100 py-3" type="submit" id="homeSubmitBtn">Send Message</button>
                                </div>
                                <div class="col-12">
                                    <div id="homeFormMessage" class="mt-3" style="display: none;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-6 wow fadeInUp order-2 order-md-2" data-wow-delay="0.1s">
                    <img class="position-relative rounded w-100" 
                        src="img/hands_shake.jpg" 
                        style="object-fit: cover; height: auto;" 
                        alt="A visual representation of cyber security">
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Contact End -->

  <!-- Footer Start -->
    <section id="footer">
        <footer class="bg-danger">
            <div class="container">              <div class="row justify-content-center">
                <!-- Quick Links removed as per request -->
                <div class="col-md-6 footer-column text-center">
                  <ul class="nav flex-column">
                    <li class="nav-item">
                      <span class="footer-title">Contact & Support</span>
                    </li>
                    <li class="nav-item d-flex align-items-center justify-content-center">
                      <span class="nav-link me-3" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-phone me-2"></i>+263 8677010124</span>
                      <span class="nav-link" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-envelope me-2"></i><EMAIL></span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row justify-content-center mt-3">
                <div class="col-lg-3 col-md-6 text-center">
                    <img src="img/logg.png" class="img-fluid">
                </div>
              </div>
            </div>
          </footer>
        <!-- Footer End -->
        
        
              <!--Back to Top-->
                <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
            </div>
    </section>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
   
</body>

<style>    /* Custom CSS to reduce image size on small screens */
    @media (max-width: 576px) {
        .img-small {
            width: 80%; /* Reduce size by 20% */
            height: auto; /* Maintain aspect ratio */
        }
    }

    /* Animations for Why Choose Us Section */
    @keyframes whyfadein {
        0% { opacity: 0; transform: translateY(30px) scale(0.98); }
        100% { opacity: 1; transform: none; }
    }
    
    @keyframes whyborderpulse {
        0%, 100% { opacity: 0.18; }
        50% { opacity: 0.32; }
    }

#whychooseus {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
    overflow: hidden;
}
#whychooseus .svg-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: 2;
    pointer-events: none;
}
.why-chooseus-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2.5rem 2.5rem;
    align-items: center;
    justify-items: center;
    position: relative;
    z-index: 3;
}
.why-chooseus-grid .feature-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.04), 0 1.5px 8px rgba(80,120,255,0.07);
    border: 1.5px solid #e3e9f7;
    padding: clamp(0.7rem, 2vw, 1.2rem) clamp(0.5rem, 2vw, 1rem);
    text-align: center;
    min-width: clamp(100px, 22vw, 180px);
    max-width: clamp(160px, 32vw, 260px);
    width: 100%;
    position: relative;
    overflow: hidden;
    animation: whyfadein 0.8s cubic-bezier(.4,0,.2,1) both;
}
.why-chooseus-grid .feature-card:hover {
    box-shadow: 0 8px 32px rgba(80,120,255,0.13), 0 2px 12px rgba(0,0,0,0.08);
    border-color: #4f8cff;
    transform: translateY(-6px) scale(1.03);
    background: linear-gradient(120deg, #f0f6ff 60%, #e3e9f7 100%);
}
.why-chooseus-grid .feature-icon {
    width: clamp(32px, 7vw, 60px);
    height: clamp(32px, 7vw, 60px);
    font-size: clamp(1rem, 2vw, 1.7rem);
    background: linear-gradient(135deg, #4f8cff 60%, #7fd7e7 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(80,120,255,0.10);
    border-radius: 50%;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.3s, background 0.3s, width 0.3s, height 0.3s, font-size 0.3s;
}
.why-chooseus-grid .feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #7fd7e7 0%, #4f8cff 100%);
    box-shadow: 0 4px 16px rgba(80,120,255,0.18);
}
.why-chooseus-grid .feature-card h4 {
    font-size: clamp(0.95rem, 2.5vw, 1.3rem);
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2a3a5e;
    letter-spacing: 0.01em;
    transition: font-size 0.3s;
}
.why-chooseus-grid .feature-card p {
    font-size: clamp(0.85rem, 2vw, 1.05rem);
    line-height: 1.5;
    margin-bottom: 0;
    color: #4f5b7d;
    transition: font-size 0.3s;
}
.why-chooseus-grid .why-center-image {
    grid-row: 1 / span 2;
    grid-column: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 180px;
    min-height: 180px;
    max-width: 320px;
    max-height: 320px;
    width: 100%;
    height: 100%;
    position: relative;
    animation: whyfadein 1.1s cubic-bezier(.4,0,.2,1) both;
    z-index: 1;
}
.why-chooseus-grid .why-center-image::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
    z-index: 0;
    opacity: 0.18;
    animation: whyborderpulse 2.5s infinite cubic-bezier(.4,0,.2,1);
}
.why-chooseus-grid .why-center-image img.circle-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    background: #f5f5f5;
    box-shadow: 0 2px 16px rgba(0,0,0,0.07);
    aspect-ratio: 1/1;
    border: 4px solid #fff;
    position: relative;
    z-index: 1;
}
@keyframes whyfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}
@keyframes whyborderpulse {
    0%, 100% { opacity: 0.18; }
    50% { opacity: 0.32; }
}

#about {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
    overflow: hidden;
    z-index: 1;
}
#about .svg-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: 2;
    pointer-events: none;
}
#about .about-card {
    background: rgba(255,255,255,0.97);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(80,120,255,0.07), 0 1.5px 8px rgba(80,120,255,0.09);
    padding: clamp(1.2rem, 3vw, 2.2rem) clamp(1rem, 3vw, 2rem);
    margin-bottom: 1.5rem;
    animation: aboutfadein 0.9s cubic-bezier(.4,0,.2,1) both;
    border: 1.5px solid #e3e9f7;
}
#about .about-card h5 {
    color: #4f8cff;
    font-weight: 700;
    letter-spacing: 0.04em;
    margin-bottom: 1rem;
}
#about .about-card p {
    color: #2a3a5e;
    font-size: clamp(1rem, 2vw, 1.13rem);
    line-height: 1.7;
    margin-bottom: 0.7rem;
}
#about .about-card .text-end a {
    color: #4f8cff;
    font-weight: 600;
    transition: color 0.2s;
}
#about .about-card .text-end a:hover {
    color: #2a3a5e;
    text-decoration: underline;
}
#about .about-img-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    animation: aboutfadein 1.1s cubic-bezier(.4,0,.2,1) both;
}
#about .about-img-wrap img {
    border-radius: 50%;
    box-shadow: 0 4px 24px rgba(80,120,255,0.13);
    border: 4px solid #fff;
    background: #f5f5f5;
    max-width: 420px;
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
    transition: box-shadow 0.3s, border 0.3s;
}
@media (max-width: 1199.98px) {
    #about .about-img-wrap img {
        max-width: 320px;
    }
}
@media (max-width: 991.98px) {
    #about .about-img-wrap img {
        max-width: 200px;
    }
}
@media (max-width: 767.98px) {
    #about .about-img-wrap img {
        max-width: 120px;
    }
    #about .about-card {
        padding: clamp(0.5rem, 2vw, 0.8rem) clamp(0.3rem, 2vw, 0.7rem);
    }
    #about .about-img-wrap {
        margin-bottom: 1.5rem;
    }
    #about .row.align-items-center {
        flex-direction: column;
    }
}
@keyframes aboutfadein {
    0% { opacity: 0; transform: translateY(30px) scale(0.98); }
    100% { opacity: 1; transform: none; }
}
@keyframes aboutborderpulse {
    0%, 100% { opacity: 0.13; }
    50% { opacity: 0.22; }
}    .fadein-about {
        opacity: 0;
        animation: aboutfadein 1.1s cubic-bezier(.4,0,.2,1) both;
    }
    
    /* Clean Why Choose Us CSS Override */
    /* ================================= */
    /* Force clean responsive design for Why Choose Us section */
    #whychooseus {
        padding: 60px 0 !important;
        background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%) !important;
        min-height: auto !important;
        overflow: visible !important;
        text-align: center !important;
        display: block !important;
        position: relative !important;
    }

    #whychooseus .container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 15px !important;
        width: auto !important;
        position: static !important;
        left: auto !important;
        transform: none !important;
        transition: none !important;
    }

    #whychooseus .section-title {
        text-align: center !important;
        margin-bottom: 50px !important;
    }

    #whychooseus .section-title h1 {
        font-size: 2.5rem !important;
        color: #2a3a5e !important;
        font-weight: 700 !important;
    }

    .why-chooseus-grid {
        display: grid !important;
        grid-template-columns: 1fr 300px 1fr !important;
        grid-template-rows: auto auto !important;
        gap: 30px !important;
        align-items: center !important;
        justify-items: center !important;
        max-width: 900px !important;
        margin: 0 auto !important;
        position: relative !important;
    }

    .why-chooseus-grid .feature-card {
        background: #fff !important;
        border-radius: 15px !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08) !important;
        border: 1px solid #e3e9f7 !important;
        padding: 25px 20px !important;
        text-align: center !important;
        width: 100% !important;
        max-width: 280px !important;
        height: auto !important;
        min-height: 220px !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        transition: all 0.3s ease !important;
    }

    .why-chooseus-grid .feature-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.12) !important;
        border-color: #4f8cff !important;
    }

    .why-chooseus-grid .feature-icon {
        width: 60px !important;
        height: 60px !important;
        font-size: 24px !important;
        background: linear-gradient(135deg, #4f8cff 60%, #7fd7e7 100%) !important;
        color: #fff !important;
        border-radius: 50% !important;
        margin-bottom: 15px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-shrink: 0 !important;
    }

    .why-chooseus-grid .feature-card h4 {
        font-size: 18px !important;
        margin-bottom: 10px !important;
        font-weight: 600 !important;
        color: #2a3a5e !important;
        line-height: 1.3 !important;
    }

    .why-chooseus-grid .feature-card p {
        font-size: 14px !important;
        line-height: 1.5 !important;
        margin-bottom: 0 !important;
        color: #4f5b7d !important;
    }

    .why-chooseus-grid .why-center-image {
        grid-row: 1 / span 2 !important;
        grid-column: 2 !important;
        width: 280px !important;
        height: 280px !important;
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .why-chooseus-grid .why-center-image::before {
        content: '' !important;
        position: absolute !important;
        inset: -8px !important;
        border-radius: 50% !important;
        background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%) !important;
        z-index: 0 !important;
        opacity: 0.15 !important;
        animation: pulseClean 3s infinite !important;
    }

    .why-chooseus-grid .why-center-image img.circle-img {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        border-radius: 50% !important;
        background: #f5f5f5 !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        aspect-ratio: 1/1 !important;
        border: 4px solid #fff !important;
        position: relative !important;
        z-index: 1 !important;
    }

    @keyframes pulseClean {
        0%, 100% { opacity: 0.15; transform: scale(1); }
        50% { opacity: 0.25; transform: scale(1.05); }
    }

    /* Responsive Design for Clean Why Choose Us */
    @media (max-width: 991.98px) {
        .why-chooseus-grid {
            grid-template-columns: 1fr 250px 1fr !important;
            gap: 25px !important;
            max-width: 800px !important;
        }
        
        .why-chooseus-grid .why-center-image {
            width: 230px !important;
            height: 230px !important;
        }
        
        .why-chooseus-grid .feature-card {
            max-width: 250px !important;
            min-height: 200px !important;
            padding: 20px 15px !important;
        }
    }

    @media (max-width: 767.98px) {
        #whychooseus {
            padding: 40px 0 !important;
        }
        
        .why-chooseus-grid {
            display: flex !important;
            flex-direction: column !important;
            gap: 25px !important;
            max-width: 100% !important;
        }
        
        .why-chooseus-grid .why-center-image {
            order: -1 !important;
            width: 200px !important;
            height: 200px !important;
            margin-bottom: 20px !important;
        }
        
        .why-chooseus-grid .feature-card {
            max-width: 100% !important;
            width: 100% !important;
            min-height: auto !important;
            padding: 25px 20px !important;
        }
        
        #whychooseus .section-title h1 {
            font-size: 2rem !important;
        }
    }

    @media (max-width: 575.98px) {
        #whychooseus .container {
            padding: 0 10px !important;
        }
        
        .why-chooseus-grid .why-center-image {
            width: 160px !important;
            height: 160px !important;
        }
        
        .why-chooseus-grid .feature-card {
            padding: 20px 15px !important;
        }
        
        .why-chooseus-grid .feature-card h4 {
            font-size: 16px !important;
        }
        
        .why-chooseus-grid .feature-card p {
            font-size: 13px !important;
        }
        
        .why-chooseus-grid .feature-icon {
            width: 50px !important;
            height: 50px !important;
            font-size: 20px !important;
        }
        
        #whychooseus .section-title h1 {
            font-size: 1.75rem !important;
        }
    }
    /* End Clean Why Choose Us CSS Override */
    /* ==================================== */
    </style>

</html>
