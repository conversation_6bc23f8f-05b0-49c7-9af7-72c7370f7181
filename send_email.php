<?php
// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type for JSON response
header('Content-Type: application/json');

// Function to sanitize input data
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Check if form was submitted via POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Initialize response array
    $response = array();
    
    // Get and sanitize form data
    $name = isset($_POST['name']) ? sanitize_input($_POST['name']) : '';
    $email = isset($_POST['email']) ? sanitize_input($_POST['email']) : '';
    $subject = isset($_POST['subject']) ? sanitize_input($_POST['subject']) : '';
    $message = isset($_POST['message']) ? sanitize_input($_POST['message']) : '';
    
    // Validation
    $errors = array();
    
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!validate_email($email)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($subject)) {
        $errors[] = "Subject is required";
    }
    
    if (empty($message)) {
        $errors[] = "Message is required";
    }
    
    // If there are validation errors
    if (!empty($errors)) {
        $response['success'] = false;
        $response['message'] = "Please fix the following errors: " . implode(", ", $errors);
        echo json_encode($response);
        exit;
    }
    
    // Email configuration
    $to_email = "<EMAIL>"; // Company email
    $from_email = $email; // Sender's email
    $from_name = $name; // Sender's name
    
    // Email subject
    $email_subject = "Contact Form: " . $subject;
    
    // Email body
    $email_body = "
    <html>
    <head>
        <title>New Contact Form Submission</title>
    </head>
    <body>
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> {$name}</p>
        <p><strong>Email:</strong> {$email}</p>
        <p><strong>Subject:</strong> {$subject}</p>
        <p><strong>Message:</strong></p>
        <p>{$message}</p>
        <hr>
        <p><small>This email was sent from the CYVIRNET contact form.</small></p>
    </body>
    </html>
    ";
    
    // Email headers
    $headers = array();
    $headers[] = "MIME-Version: 1.0";
    $headers[] = "Content-type: text/html; charset=UTF-8";
    $headers[] = "From: {$from_name} <{$from_email}>";
    $headers[] = "Reply-To: {$from_email}";
    $headers[] = "X-Mailer: PHP/" . phpversion();
    
    // Send email
    if (mail($to_email, $email_subject, $email_body, implode("\r\n", $headers))) {
        $response['success'] = true;
        $response['message'] = "Thank you! Your message has been sent successfully. We will get back to you soon.";
    } else {
        $response['success'] = false;
        $response['message'] = "Sorry, there was an error sending your message. Please try again later or contact us directly.";
    }
    
    echo json_encode($response);
    
} else {
    // If not a POST request
    $response['success'] = false;
    $response['message'] = "Invalid request method";
    echo json_encode($response);
}
?>
