<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>CYVIRNET | About Us</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

   <!-- Favicon -->
   <link rel="icon" type="image/x-icon" href="https://images.squarespace-cdn.com/content/v1/659c01d1b9d869462047df7b/6b205281-0425-4502-8b43-b4622a33598a/favicon.ico?format=100w">


    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
    <style>.about-section {padding: 90px 0px;}.service-section{padding: 90px 0px;}.team-sections{padding: 70px 0px;}.why-sections{padding-top: 70px;
     }.testimonials-sections{padding-top: 70px;}.our-portfolio-sections{padding-top: 70px;}.col-lg-7 {text-align: center;}                #footer .nav-link {
                        color: #d2c9c9; /* Change link color to white */
                        font-family: 'Montserrat', sans-serif;
                        font-size: large;
                    }</style>
</head>

    <body class="bg-white">
        <div class="bg-white p-0">
            <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <img src="img/CYVIRNET Logo-01.png" style="width: 100px; height: auto;" alt="Loading...">
        </div>
        </div>
        <!-- Spinner End -->


     <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow fixed-top w-100">        <a href="index.html" class="navbar-brand d-flex align-items-center text-center px-4 px-lg-5">
          <img src="img/Log.jpg" class="img-fluid" alt="Logo" style="width: 48%; height: auto;">
        </a>
        <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link active">Home</a>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">About</a>
                    <div class="dropdown-menu fade-down m-0">
                        <a href="about.html" class="dropdown-item">About Us</a>
                        <a href="Our_Portifolio.html" class="dropdown-item">Our Portfolio</a>
                        <a href="testimonials.html" class="dropdown-item">Testimonial</a>
                    </div>
                </div>
                <a href="service.html" class="nav-item nav-link">Services</a>
                <a href="team.html" class="nav-item nav-link">Our Team</a>
                <div class="pe-5 py-2">
                    <a href="contact.html">
                        <button class="button rounded px-lg-5 d-md-block">Contact Us</button>
                    </a>
                </div>
            </div>
        </div>
    </nav>    <!-- Navbar End -->

    <!-- Enhanced Hero Section for About Page -->
    <section class="about-hero-section">
        <div class="hero-background"></div>
        <div class="hero-overlay"></div>
        
        <!-- Animated particles -->
        <div class="hero-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="container">
            <div class="row align-items-center min-vh-60">
                <div class="col-lg-8 col-md-10 mx-auto text-center">
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-building me-2"></i>
                            <span>About CYVIRNET</span>
                        </div>
                        <h1 class="about-hero-title">
                            Who We Are
                        </h1>
                        <p class="about-hero-description">
                            CYVIRNET is a leading provider of cybersecurity, network, and cloud solutions committed to innovation and world-class technology services. We view technology as an interconnected ecosystem that includes consulting, installation, maintenance, security, and scalability.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- End Enhanced Hero Section -->

    <style>
    /* === ABOUT HERO SECTION === */    .about-hero-section {
        position: relative;
        min-height: 60vh;
        display: flex;
        align-items: center;
        overflow: hidden;
        padding: 120px 0 40px; /* Increased padding to prevent content cutoff */
        background: linear-gradient(rgba(26,35,126,0.7), rgba(13,71,161,0.6)), url('img/**********.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-image: 
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
        background-size: 100px 100px, 150px 150px, 200px 200px;
    }

    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.1);
        z-index: 2;
    }

    .hero-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
    }

    .particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background: rgba(255,255,255,0.6);
        border-radius: 50%;
        animation: particleFloat 6s ease-in-out infinite;
    }

    .particle:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: -1s;
        animation-duration: 8s;
    }

    .particle:nth-child(2) {
        top: 60%;
        left: 80%;
        animation-delay: -3s;
        animation-duration: 10s;
    }

    .particle:nth-child(3) {
        top: 40%;
        left: 60%;
        animation-delay: -2s;
        animation-duration: 7s;
    }

    .particle:nth-child(4) {
        top: 80%;
        left: 20%;
        animation-delay: -4s;
        animation-duration: 9s;
    }

    .particle:nth-child(5) {
        top: 30%;
        left: 90%;
        animation-delay: -1.5s;
        animation-duration: 6s;
    }    .hero-content {
        position: relative;
        z-index: 10;
        animation: heroContentSlideUp 1.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        margin-top: 60px; /* Increased margin for better spacing */
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        background: #ffffff;
        border-radius: 0;
        border: 1px solid #dee2e6;
        padding: 8px 18px;
        font-size: 0.9rem;
        color: #2a3a5e;
        font-weight: 500;
        margin-bottom: 1.8rem;
        animation: heroContentSlideUp 1.4s cubic-bezier(0.4, 0, 0.2, 1) both;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .about-hero-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        line-height: 1.1;
        color: #ffffff;
        margin-bottom: 1.4rem;
        text-shadow: 0 2px 20px rgba(0,0,0,0.3);
        text-align: center;
        animation: heroLineSlideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
        animation-delay: 0.2s;
    }    .about-hero-description {
        font-size: clamp(1.1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        color: #ffffff;
        margin-bottom: 2rem; /* Increased margin for better spacing */
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        animation: heroContentSlideUp 1.6s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    /* === ANIMATIONS === */
    @keyframes particleFloat {
        0%, 100% { transform: translateY(0) translateX(0); opacity: 0.6; }
        25% { transform: translateY(-15px) translateX(8px); opacity: 1; }
        50% { transform: translateY(-8px) translateX(-4px); opacity: 0.8; }
        75% { transform: translateY(-12px) translateX(12px); opacity: 0.9; }
    }

    @keyframes heroContentSlideUp {
        0% { opacity: 0; transform: translateY(50px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes heroLineSlideUp {
        0% { opacity: 0; transform: translateY(40px); }
        100% { opacity: 1; transform: translateY(0); }
    }    /* === RESPONSIVE === */
    @media (max-width: 991.98px) {
        .about-hero-section {
            min-height: 55vh; /* Increased height */
            padding: 100px 0 30px; /* Increased padding */
        }
        .hero-content {
            margin-top: 50px; /* Increased margin */
        }
    }

    @media (max-width: 767.98px) {
        .about-hero-section {
            min-height: 50vh; /* Increased height */
            padding: 90px 0 25px; /* Increased padding */
        }
        .hero-content {
            margin-top: 40px; /* Increased margin */
        }
    }

    @media (max-width: 575.98px) {
        .about-hero-section {
            min-height: 45vh; /* Increased height */
            padding: 110px 0 20px; /* Increased padding */
        }
        .hero-content {
            margin-top: 30px; /* Increased margin */
        }
        .hero-badge {
            font-size: 0.8rem;
            padding: 6px 14px;
        }
    }/* Extra small mobile screens */
    @media (max-width: 480px) {
        .about-hero-section {
            padding: 95px 0 8px; /* Reduced padding */
        }
        .hero-content {
            margin-top: 15px; /* Reduced margin */
        }
    }

    @media (max-width: 360px) {
        .about-hero-section {
            padding: 100px 0 8px; /* Reduced padding */
        }
        .hero-content {
            margin-top: 10px; /* Reduced margin */
        }
    }
    </style>

    <!-- Enhanced About Section -->
    <section class="about-enhanced-section">
        <div class="about-wave-top">
            <svg viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                <path d="M0,30 C360,60 1080,0 1440,30 L1440,60 L0,60 Z" fill="#f8fafc"/>
            </svg>
        </div>          <div class="container py-3"> <!-- Reduced padding -->
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="about-content-card">
                        <div class="partnership-image-header">
                            <img src="img/hands_shake.jpg" alt="Technology Partnership" class="partnership-img">
                            <div class="image-badge">
                                <i class="fas fa-handshake me-2"></i>
                                <span>Our Partnership Approach</span>
                            </div>
                        </div>
                        
                        <div class="card-content-body">
                            <div class="row">
                                <div class="col-lg-8 mx-auto text-center">
                                    <h2 class="about-section-title">Building Lasting Technology Partnerships</h2>
                                    <p class="about-description">
                                        As partners, we prioritize understanding our clients' visions and needs over presenting our own. 
                                        We value timeliness, direct communication, and practical solutions, complemented by meaningful collaboration.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="about-features">
                                <div class="row g-4">
                                    <div class="col-lg-4">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="fas fa-network-wired"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h5>Quality Network Solutions</h5>
                                                <p>Enterprise-grade networking infrastructure designed for reliability and performance.</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-4">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="fas fa-cloud"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h5>Cloud Solutions</h5>
                                                <p>Scalable cloud services that adapt to your business growth and requirements.</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-4">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="fas fa-cogs"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h5>Consultation & Implementation</h5>
                                                <p>Expert guidance from planning to deployment, ensuring seamless integration.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values & Culture Section -->
    <section class="values-culture-section">
        <div class="container py-5">
            <div class="text-center mb-5">
                <div class="section-badge">
                    <i class="fas fa-heart me-2"></i>
                    <span>Our Foundation</span>
                </div>
                <h2 class="section-title">Values That Drive Excellence</h2>
                <p class="section-subtitle">
                    Built on a foundation of integrity, innovation, and client-focused solutions
                </p>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="value-card purpose-card">
                        <div class="card-icon">
                            <img src="img/core_values.png" alt="Purpose & Values" class="value-icon">
                        </div>
                        <div class="card-content">
                            <h4>Our Purpose & Values</h4>
                            <p>At the core of our company lies a strong sense of purpose and unwavering values, driving us to provide solutions that align with principles of integrity, transparency, and client satisfaction.</p>
                        </div>
                        <div class="card-decoration"></div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="value-card commitment-card">
                        <div class="card-icon">
                            <div class="commitment-visual">
                                <i class="fas fa-handshake"></i>
                            </div>
                        </div>
                        <div class="card-content">
                            <h4>Our Commitment to You</h4>
                            <p>We are dedicated to fostering enduring partnerships by prioritizing your unique requirements and ensuring steadfast commitment to delivering high-quality, reliable IT services.</p>
                        </div>
                        <div class="card-decoration"></div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="value-card innovation-card">
                        <div class="card-icon">
                            <div class="innovation-visual">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                        </div>
                        <div class="card-content">
                            <h4>A Culture of Innovation</h4>
                            <p>Embracing a culture that thrives on innovation, we encourage creative thinking and continuous improvement, staying ahead of the technological curve.</p>
                        </div>
                        <div class="card-decoration"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Certifications Section -->
    <section class="certifications-section">
        <div class="cert-wave-top">
            <svg viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                <path d="M0,30 C360,0 1080,60 1440,30 L1440,0 L0,0 Z" fill="#ffffff"/>
            </svg>
        </div>
          <div class="container py-3"> <!-- Reduced padding -->
            <div class="row align-items-center">
                <div class="col-lg-5 mb-3 mb-lg-0"> <!-- Reduced margin -->
                    <div class="cert-content">
                        <div class="cert-badge">
                            <i class="fas fa-award me-2"></i>
                            <span>Professional Excellence</span>
                        </div>
                        <h2 class="cert-title">Certified Excellence</h2>
                        <h3 class="cert-subtitle">Industry-Leading Expertise</h3>                        <p class="cert-description">
                            Our team holds industry-leading certifications to ensure we deliver the highest quality 
                            cybersecurity, networking, and cloud solutions to our clients. Each certification represents 
                            our commitment to maintaining cutting-edge expertise.
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-7">
                    <div class="certifications-grid">
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/aws.png" alt="AWS Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Amazon Web Services</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/juniper.png" alt="Juniper Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Juniper Networks</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/ccna.png" alt="Cisco CCNA Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Cisco CCNA</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/oscp.png" alt="OSCP Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Offensive Security</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/ceh.png" alt="CEH Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Certified Ethical Hacker</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/cisa.png" alt="CISA Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">Certified Information Systems Auditor</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/giac.png" alt="GIAC Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">GIAC Security</div>
                        </div>
                        
                        <div class="cert-item">
                            <div class="cert-logo">
                                <img src="img/brands/comp.png" alt="CompTIA Certification" class="cert-img">
                            </div>
                            <div class="cert-tooltip">CompTIA Security+</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cert-wave-bottom">
            <svg viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                <path d="M0,30 C360,60 1080,0 1440,30 L1440,60 L0,60 Z" fill="#ffffff"/>
            </svg>
        </div>
    </section>

    <!-- Enhanced Styling -->
    <style>
    /* === ENHANCED ABOUT SECTION === */
    .about-enhanced-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e3e9f7 100%);
        position: relative;
        overflow: hidden;
    }

    .about-wave-top {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 60px;
        z-index: 2;
    }    .about-content-card {
        background: #ffffff;
        border-radius: 0;
        padding: 0;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        border: 1px solid #dee2e6;
        position: relative;
        z-index: 3;
        overflow: hidden;
    }    .partnership-image-header {
        position: relative;
        height: 160px; /* Reduced height */
        overflow: hidden;
        border-radius: 0;
    }

    .partnership-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .about-content-card:hover .partnership-img {
        transform: scale(1.05);
    }

    .image-badge {
        position: absolute;
        top: 20px;
        left: 20px;
        display: inline-flex;
        align-items: center;
        background: rgba(79,140,255,0.9);
        backdrop-filter: blur(10px);
        color: white;
        padding: 8px 20px;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(79,140,255,0.3);
        border: 1px solid rgba(255,255,255,0.2);
    }    .card-content-body {
        padding: 25px; /* Reduced padding */
    }

    .about-badge {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
        color: white;
        padding: 8px 20px;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(79,140,255,0.3);
    }

    .about-section-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 800;
        color: #2a3a5e;
        margin-bottom: 20px;
        line-height: 1.2;
    }

    .about-description {
        font-size: 1.1rem;
        color: #4f5b7d;
        line-height: 1.7;
        margin-bottom: 30px;
    }    .about-features {
        margin-top: 25px; /* Reduced margin */
    }

    .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px; /* Reduced gap */
        padding: 20px 15px; /* Reduced padding */
        background: rgba(255,255,255,0.5);
        border-radius: 15px;
        transition: all 0.3s ease;
        border: 1px solid rgba(79,140,255,0.1);
        height: 100%;
    }

    .feature-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(79,140,255,0.15);
        border-color: rgba(79,140,255,0.3);
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .feature-content h5 {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2a3a5e;
        margin-bottom: 8px;
    }

    .feature-content p {
        font-size: 0.95rem;
        color: #4f5b7d;
        margin: 0;
        line-height: 1.5;
    }

    /* Visual Wrapper */
    .about-visual-wrapper {
        position: relative;
    }

    .about-main-image {
        position: relative;
        overflow: hidden;
        border-radius: 20px;
        box-shadow: 0 15px 50px rgba(0,0,0,0.15);
    }

    .about-main-image img {
        transition: transform 0.3s ease;
    }

    .about-main-image:hover img {
        transform: scale(1.05);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(79,140,255,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .about-main-image:hover .image-overlay {
        opacity: 1;
    }

    .play-button {
        width: 70px;
        height: 70px;
        background: rgba(255,255,255,0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4f8cff;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .play-button:hover {
        transform: scale(1.1);
        background: white;
    }

    .floating-stats {
        position: absolute;
        bottom: -30px;
        right: 20px;
        display: flex;
        gap: 15px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(79,140,255,0.1);
        min-width: 100px;
    }

    .stat-card h3 {
        font-size: 1.8rem;
        font-weight: 800;
        color: #4f8cff;
        margin-bottom: 5px;
    }

    .stat-card p {
        font-size: 0.85rem;
        color: #4f5b7d;
        margin: 0;
        font-weight: 600;
    }    /* === VALUES & CULTURE SECTION === */
    .values-culture-section {
        padding: 50px 0; /* Reduced padding */
        background: white;
    }

    .section-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(79,140,255,0.1);
        color: #4f8cff;
        padding: 8px 20px;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 20px;
        border: 1px solid rgba(79,140,255,0.2);
    }

    .section-title {
        font-size: clamp(2rem, 5vw, 3rem);
        font-weight: 800;
        color: #2a3a5e;
        margin-bottom: 15px;
    }

    .section-subtitle {
        font-size: 1.2rem;
        color: #4f5b7d;
        max-width: 600px;
        margin: 0 auto;
    }    .value-card {
        background: white;
        border-radius: 20px;
        padding: 25px 20px; /* Reduced padding */
        height: 100%;
        border: 1px solid #e3e9f7;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .value-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #4f8cff 0%, #7fd7e7 100%);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .value-card:hover::before {
        transform: scaleX(1);
    }

    .value-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        border-color: rgba(79,140,255,0.3);
    }    .card-icon {
        margin-bottom: 15px; /* Reduced margin */
        text-align: center;
    }

    .value-icon {
        width: 60px; /* Reduced size */
        height: 60px;
        object-fit: contain;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .commitment-visual,
    .innovation-visual {
        width: 60px; /* Reduced size */
        height: 60px;
        background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: white;
        font-size: 1.5rem; /* Reduced font size */
        box-shadow: 0 8px 25px rgba(79,140,255,0.3);
    }

    .card-content h4 {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2a3a5e;
        margin-bottom: 15px;
    }

    .card-content p {
        color: #4f5b7d;
        line-height: 1.6;
        font-size: 1rem;
    }

    .card-decoration {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(79,140,255,0.05) 0%, rgba(127,215,231,0.05) 100%);
        border-radius: 50%;
        transform: translate(30%, 30%);
    }

    /* === CERTIFICATIONS SECTION === */
    .certifications-section {
        background: linear-gradient(135deg, #1a237e 0%, #0d47a1 100%);
        position: relative;
        color: white;
    }

    .cert-wave-top,
    .cert-wave-bottom {
        position: absolute;
        left: 0;
        width: 100%;
        height: 60px;
        z-index: 2;
    }

    .cert-wave-top {
        top: 0;
    }

    .cert-wave-bottom {
        bottom: 0;
    }

    .cert-content {
        position: relative;
        z-index: 3;
    }

    .cert-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        color: white;
        padding: 8px 20px;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 20px;
        border: 1px solid rgba(255,255,255,0.2);
    }    .cert-title {
        font-size: clamp(2rem, 5vw, 3rem);
        font-weight: 800;
        margin-bottom: 10px;
        color: white;
    }

    .cert-subtitle {
        font-size: 1.5rem;
        font-weight: 600;
        color: rgba(255,255,255,0.9);
        margin-bottom: 20px;
    }

    .cert-description {
        font-size: 1.1rem;
        line-height: 1.7;
        color: rgba(255,255,255,0.8);
        margin-bottom: 40px;
    }

    .cert-stats {
        display: flex;
        gap: 40px;
        margin-top: 30px;
    }

    .cert-stat {
        text-align: center;
    }

    .cert-stat h4 {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #ffffff 0%, #7fd7e7 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
    }

    .cert-stat p {
        color: rgba(255,255,255,0.8);
        font-weight: 600;
        margin: 0;
    }    .certifications-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px; /* Reduced gap */
        position: relative;
        z-index: 3;
    }

    .cert-item {
        position: relative;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 0;
        padding: 15px; /* Reduced padding */
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cert-item:hover {
        transform: translateY(-10px);
        background: #f8f9fa;
        border-color: #cccccc;
        box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    }    .cert-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px; /* Reduced height */
        margin-bottom: 10px; /* Reduced margin */
    }

    .cert-img {
        max-width: 50px; /* Reduced size */
        max-height: 50px;
        object-fit: contain;
        transition: all 0.3s ease;
    }

    .cert-item:hover .cert-img {
        transform: scale(1.1);
    }

    .cert-tooltip {
        position: absolute;
        bottom: -45px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .cert-item:hover .cert-tooltip {
        opacity: 1;
        bottom: -35px;
    }    /* === RESPONSIVE DESIGN === */
    @media (max-width: 991.98px) {
        .card-content-body {
            padding: 20px; /* Reduced padding */
        }
        
        .partnership-image-header {
            height: 140px; /* Reduced height */
        }
        
        .certifications-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 12px; /* Reduced gap */
        }
        
        .cert-stats {
            gap: 20px; /* Reduced gap */
        }
    }

    @media (max-width: 767.98px) {
        .card-content-body {
            padding: 18px; /* Reduced padding */
        }
        
        .partnership-image-header {
            height: 120px; /* Reduced height */
        }
        
        .feature-item {
            text-align: center;
            padding: 15px 10px; /* Reduced padding */
        }
        
        .certifications-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px; /* Reduced gap */
        }
        
        .cert-stats {
            flex-direction: column;
            gap: 15px; /* Reduced gap */
            text-align: center;
        }
        
        .value-card {
            padding: 20px 15px; /* Reduced padding */
        }
    }

    @media (max-width: 575.98px) {
        .partnership-image-header {
            height: 100px; /* Reduced height */
        }
        
        .card-content-body {
            padding: 15px; /* Reduced padding */
        }
        
        .image-badge {
            top: 10px; /* Reduced positioning */
            left: 10px;
            padding: 4px 12px; /* Reduced padding */
            font-size: 0.75rem; /* Reduced font size */
        }
        
        .about-features .row {
            flex-direction: column;
        }
        
        .certifications-grid {
            grid-template-columns: 1fr;
            max-width: 280px; /* Reduced width */
            margin: 0 auto;
        }
        
        .cert-item {
            padding: 12px; /* Reduced padding */
        }
    }
    </style>
  <!-- Footer Start -->
  <section id="footer">
        <footer class="bg-danger">
            <div class="container">              <div class="row justify-content-center">
                <!-- Quick Links removed as per request -->
                <div class="col-md-6 footer-column text-center">
                  <ul class="nav flex-column">
                    <li class="nav-item">
                      <span class="footer-title">Contact & Support</span>
                    </li>
                    <li class="nav-item d-flex align-items-center justify-content-center">
                      <span class="nav-link me-3" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-phone me-2"></i>+263 8677010124</span>
                      <span class="nav-link" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-envelope me-2"></i><EMAIL></span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row justify-content-center mt-3">
                <div class="col-lg-3 col-md-6 text-center">
                    <img src="img/CYVIRNET Logo-06.png" class="img-fluid">
                </div>
              </div>              
            </div>
          </footer>
        <!-- Footer End -->
        
        
              <!--Back to Top-->
                <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
            </div>
    </section>
<!-- Footer End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
</body>
</html>
