<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Submissions - CYVIRNET</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #4f8cff 0%, #7fd7e7 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .submission {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #4f8cff;
        }
        .submission-header {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #2a3a5e;
        }
        .field {
            margin-bottom: 10px;
        }
        .field-label {
            font-weight: bold;
            color: #4f5b7d;
            display: inline-block;
            width: 80px;
        }
        .field-value {
            color: #2a3a5e;
        }
        .message-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #4f8cff;
        }
        .no-submissions {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            margin-right: 10px;
        }
        .refresh-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📧 Contact Form Submissions</h1>
        <p>CYVIRNET - Localhost Development Mode</p>
    </div>

    <?php
    $log_file = 'contact_submissions.txt';
    
    // Handle clear submissions
    if (isset($_POST['clear_submissions'])) {
        if (file_exists($log_file)) {
            unlink($log_file);
            echo '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
                    ✅ All submissions have been cleared!
                  </div>';
        }
    }
    
    if (file_exists($log_file) && filesize($log_file) > 0) {
        $content = file_get_contents($log_file);
        $submissions = explode(str_repeat("=", 50), $content);
        
        // Remove empty entries
        $submissions = array_filter($submissions, function($submission) {
            return trim($submission) !== '';
        });
        
        if (!empty($submissions)) {
            echo '<div style="text-align: center; margin-bottom: 20px;">';
            echo '<form method="post" style="display: inline;">';
            echo '<button type="submit" name="refresh" class="refresh-btn">🔄 Refresh</button>';
            echo '</form>';
            echo '<form method="post" style="display: inline;" onsubmit="return confirm(\'Are you sure you want to clear all submissions?\');">';
            echo '<button type="submit" name="clear_submissions" class="clear-btn">🗑️ Clear All</button>';
            echo '</form>';
            echo '</div>';
            
            echo '<h2>📋 Total Submissions: ' . count($submissions) . '</h2>';
            
            // Display submissions in reverse order (newest first)
            $submissions = array_reverse($submissions);
            
            foreach ($submissions as $index => $submission) {
                if (trim($submission)) {
                    echo '<div class="submission">';
                    
                    // Parse the submission
                    $lines = explode("\n", trim($submission));
                    $datetime = '';
                    $name = '';
                    $email = '';
                    $subject = '';
                    $message = '';
                    
                    foreach ($lines as $line) {
                        if (strpos($line, 'Form Submission - ') !== false) {
                            $datetime = str_replace('Form Submission - ', '', $line);
                        } elseif (strpos($line, 'Name: ') === 0) {
                            $name = substr($line, 6);
                        } elseif (strpos($line, 'Email: ') === 0) {
                            $email = substr($line, 7);
                        } elseif (strpos($line, 'Subject: ') === 0) {
                            $subject = substr($line, 9);
                        } elseif (strpos($line, 'Message: ') === 0) {
                            $message = substr($line, 9);
                        }
                    }
                    
                    echo '<div class="submission-header">📅 ' . htmlspecialchars($datetime) . '</div>';
                    echo '<div class="field"><span class="field-label">👤 Name:</span> <span class="field-value">' . htmlspecialchars($name) . '</span></div>';
                    echo '<div class="field"><span class="field-label">📧 Email:</span> <span class="field-value">' . htmlspecialchars($email) . '</span></div>';
                    echo '<div class="field"><span class="field-label">📝 Subject:</span> <span class="field-value">' . htmlspecialchars($subject) . '</span></div>';
                    echo '<div class="field"><span class="field-label">💬 Message:</span></div>';
                    echo '<div class="message-content">' . nl2br(htmlspecialchars($message)) . '</div>';
                    echo '</div>';
                }
            }
        } else {
            echo '<div class="no-submissions">
                    <h3>📭 No submissions yet</h3>
                    <p>Form submissions will appear here when users submit the contact form.</p>
                  </div>';
        }
    } else {
        echo '<div class="no-submissions">
                <h3>📭 No submissions yet</h3>
                <p>Form submissions will appear here when users submit the contact form.</p>
                <p><strong>To test:</strong> Go to your website and submit the contact form on the home page or contact page.</p>
              </div>';
    }
    ?>

    <div style="margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 8px;">
        <h3>🔧 Development Notes:</h3>
        <ul>
            <li><strong>Localhost Mode:</strong> Form submissions are saved to <code>contact_submissions.txt</code></li>
            <li><strong>Production Mode:</strong> When deployed to a live server, emails will be sent to <code><EMAIL></code></li>
            <li><strong>File Location:</strong> <code><?php echo realpath($log_file) ?: $log_file; ?></code></li>
        </ul>
    </div>
</body>
</html>
