<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>CYVIRNET | Our Services</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://images.squarespace-cdn.com/content/v1/659c01d1b9d869462047df7b/6b205281-0425-4502-8b43-b4622a33598a/favicon.ico?format=100w">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>.about-section {padding: 90px 0px;}.service-section{padding: 90px 0px;}.team-sections{padding: 70px 0px;}.why-sections{padding-top: 70px;
    }.testimonials-sections{padding-top: 70px;}.our-portfolio-sections{padding-top: 70px;}.col-lg-7 {text-align: center;}                   #footer .nav-link {
                       color: #d2c9c9; /* Change link color to white */
                       font-family:'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
                   }</style>
</head>

    <body class="bg-white">
        <div class="bg-white p-0">
            <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <img src="img/CYVIRNET Logo-01.png" style="width: 100px; height: auto;" alt="Loading...">
        </div>
        </div>
        <!-- Spinner End -->


   <!-- Navbar Start -->
   <nav class="navbar navbar-expand-lg bg-white navbar-light shadow fixed-top w-100">    <a href="index.html" class="navbar-brand d-flex align-items-center text-center px-4 px-lg-5">
        <img src="img/Log.jpg" class="img-fluid" alt="Logo" style="width: 48%; height: auto;">
    </a>
    <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto p-4 p-lg-0">
            <a href="index.html" class="nav-item nav-link active">Home</a>
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">About</a>
                <div class="dropdown-menu fade-down m-0">
                    <a href="about.html" class="dropdown-item">About Us</a>
                    <a href="Our_Portifolio.html" class="dropdown-item">Our Portfolio</a>
                    <a href="testimonials.html" class="dropdown-item">Testimonial</a>
                </div>
            </div>
            <a href="service.html" class="nav-item nav-link">Services</a>
            <a href="team.html" class="nav-item nav-link">Our Team</a>
            <div class="pe-5 py-2">
                <a href="contact.html">
                    <button class="button rounded px-lg-5 d-md-block">Contact Us</button>
                </a>
            </div>
        </div>
    </div>
</nav>
<!-- Navbar End -->

    <!-- Enhanced Hero Section for Services Page -->
    <section class="services-hero-section">
        <div class="hero-background"></div>
        <div class="hero-overlay"></div>
        
        <!-- Animated particles -->
        <div class="hero-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="container">
            <div class="row align-items-center min-vh-60">
                <div class="col-lg-8 col-md-10 mx-auto text-center">
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-cogs me-2"></i>
                            <span>Professional Services</span>
                        </div>
                        <h1 class="services-hero-title">
                            Our Services
                        </h1>
                        <p class="services-hero-description">
                            At CYVIRNET, we offer a range of professional services that cater to your networking, cybersecurity and cloud needs. Our experienced team is dedicated to providing you with customized solutions that meet your specific business requirements. Contact us today to find out how we can help your business grow.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- End Enhanced Hero Section -->

    <style>
    /* === SERVICES HERO SECTION === */    .services-hero-section {
        position: relative;
        min-height: 60vh;
        display: flex;
        align-items: center;
        overflow: hidden;
        padding: 120px 0 30px; /* Increased top padding to account for fixed navbar */
        background: linear-gradient(rgba(26,35,126,0.7), rgba(13,71,161,0.6)), url('img/**********.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-image: 
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 2px, transparent 2px);
        background-size: 100px 100px, 150px 150px, 200px 200px;
    }

    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.1);
        z-index: 2;
    }

    .hero-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
    }

    .particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background: rgba(255,255,255,0.6);
        border-radius: 50%;
        animation: particleFloat 6s ease-in-out infinite;
    }

    .particle:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: -1s;
        animation-duration: 8s;
    }

    .particle:nth-child(2) {
        top: 60%;
        left: 80%;
        animation-delay: -3s;
        animation-duration: 10s;
    }

    .particle:nth-child(3) {
        top: 40%;
        left: 60%;
        animation-delay: -2s;
        animation-duration: 7s;
    }

    .particle:nth-child(4) {
        top: 80%;
        left: 20%;
        animation-delay: -4s;
        animation-duration: 9s;
    }

    .particle:nth-child(5) {
        top: 30%;
        left: 90%;
        animation-delay: -1.5s;
        animation-duration: 6s;
    }    .hero-content {
        position: relative;
        z-index: 10;
        animation: heroContentSlideUp 1.2s cubic-bezier(0.4, 0, 0.2, 1) both;
        margin-top: 80px; /* Add top margin to push content below navbar */
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 50px;
        padding: 8px 18px;
        font-size: 0.9rem;
        color: #ffffff;
        font-weight: 500;
        margin-bottom: 1.8rem;
        animation: heroContentSlideUp 1.4s cubic-bezier(0.4, 0, 0.2, 1) both;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .services-hero-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: 800;
        line-height: 1.1;
        color: #ffffff;
        margin-bottom: 1.4rem;
        text-shadow: 0 2px 20px rgba(0,0,0,0.3);
        text-align: center;
        animation: heroLineSlideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
        animation-delay: 0.2s;
    }

    .services-hero-description {
        font-size: clamp(1.1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        color: #ffffff;
        margin-bottom: 2.5rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        animation: heroContentSlideUp 1.6s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    /* === ANIMATIONS === */
    @keyframes particleFloat {
        0%, 100% { transform: translateY(0) translateX(0); opacity: 0.6; }
        25% { transform: translateY(-15px) translateX(8px); opacity: 1; }
        50% { transform: translateY(-8px) translateX(-4px); opacity: 0.8; }
        75% { transform: translateY(-12px) translateX(12px); opacity: 0.9; }
    }

    @keyframes heroContentSlideUp {
        0% { opacity: 0; transform: translateY(50px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    @keyframes heroLineSlideUp {
        0% { opacity: 0; transform: translateY(40px); }
        100% { opacity: 1; transform: translateY(0); }
    }    /* === RESPONSIVE === */
    @media (max-width: 991.98px) {
        .services-hero-section {
            min-height: 55vh;
            padding: 100px 0 20px; /* Maintain navbar offset */
        }
        .hero-content {
            margin-top: 60px; /* Adjusted margin for medium screens */
        }
    }

    @media (max-width: 767.98px) {
        .services-hero-section {
            min-height: 50vh;
            padding: 90px 0 15px; /* Maintain navbar offset for tablets */
        }
        .hero-content {
            margin-top: 50px; /* Adjusted margin for tablets */
        }
    }

    @media (max-width: 575.98px) {
        .services-hero-section {
            min-height: 45vh;
            padding: 110px 0 10px; /* Increased mobile padding to prevent cutoff */
        }
        .hero-content {
            margin-top: 40px; /* Adjusted margin for mobile */
        }
        .hero-badge {
            font-size: 0.8rem;
            padding: 6px 14px;
        }
    }

    /* Extra small mobile screens */
    @media (max-width: 480px) {
        .services-hero-section {
            padding: 120px 0 10px; /* Even more padding for very small screens */
        }
        .hero-content {
            margin-top: 30px; /* Reduced margin for very small screens */
        }
    }

    @media (max-width: 360px) {
        .services-hero-section {
            padding: 130px 0 10px; /* Maximum padding for smallest screens */
        }
        .hero-content {
            margin-top: 20px; /* Minimal margin for smallest screens */
        }
    }
    </style>    <!-- Service Card Image Fixes -->
    <style>
    /* === SERVICE CARD IMAGE FIXES === */    .services-bar .card {
        overflow: hidden;
        border-radius: 20px !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        padding: 0;
        border: none;
    }

    .services-bar .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }.services-bar .card-img {
        height: 220px;
        overflow: hidden;
        border-radius: 20px 20px 0 0 !important;
        position: relative;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .services-bar .card-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 20px 20px 0 0 !important;
        transition: transform 0.3s ease;
        display: block;
        margin: 0;
        padding: 0;
    }

    .services-bar .card:hover .card-img img {
        transform: scale(1.05);
    }

    .services-bar .card-body {
        padding: 1.5rem;
        border-radius: 0 0 20px 20px !important;
        display: flex;
        flex-direction: column;
        height: auto;
        min-height: 180px;
    }

    .services-bar .card-body h4 {
        color: #1273eb;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        line-height: 1.3;
    }

    .services-bar .card-body .card-text {
        color: #666;
        line-height: 1.6;
        font-size: 0.95rem;
        flex-grow: 1;
        margin-bottom: 0;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .services-bar .card-img {
            height: 200px;
        }
        
        .services-bar .card-body {
            padding: 1.25rem;
            min-height: 160px;
        }
        
        .services-bar .card-body h4 {
            font-size: 1.1rem;
        }
        
        .services-bar .card-body .card-text {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .services-bar .card-img {
            height: 180px;
        }
        
        .services-bar .card-body {
            padding: 1rem;
            min-height: 140px;
        }
    }
    </style>

    <!-- Critical Service Card Image Fixes - Override all existing styles -->
    <style>
    /* === FORCE FULL WIDTH SERVICE CARD IMAGES === */
    div.services-bar div.container div.row div.col-lg-4 div.card div.card-img {
        height: 220px !important;
        overflow: hidden !important;
        border-radius: 20px 20px 0 0 !important;
        position: relative !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: #f8f9fa !important;
    }

    div.services-bar div.container div.row div.col-lg-4 div.card div.card-img img.img-fluid {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        object-position: center !important;
        border-radius: 20px 20px 0 0 !important;
        transition: transform 0.3s ease !important;
        display: block !important;
        margin: 0 !important;
        padding: 0 !important;
        max-width: none !important;
        min-width: 100% !important;
        border: none !important;
        outline: none !important;
        box-sizing: border-box !important;
    }

    div.services-bar div.container div.row div.col-lg-4 div.card {
        overflow: hidden !important;
        border-radius: 20px !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
        transition: all 0.3s ease !important;
        padding: 0 !important;
        border: none !important;
        margin: 0 0 1.5rem 0 !important;
    }

    div.services-bar div.container div.row div.col-lg-4 div.card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    div.services-bar div.container div.row div.col-lg-4 div.card:hover div.card-img img {
        transform: scale(1.05) !important;
    }
    </style>

    <!--Start services-->
<div class="services-bar">
    <div class="container section-heading">
        <!-- Services Section -->
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/nastya-dulhiier-OKOOGO578eo-unsplash.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Network Solutions</h4>
                        <p class="card-text">Our network solutions are designed 
                            to ensure that your business stays connected and secure. 
                            We offer a range of network solutions that include network 
                            design, network installation, and network security. From 
                            LAN/WAN setups to wireless networks, we optimize connectivity 
                            to keep your operations running smoothly.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/home.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Cyber Security Services</h4>
                        <p class="card-text">Safeguard your digital assets with our 
                            comprehensive cybersecurity services. We offer threat 
                            detection, risk assessment, and proactive measures to 
                            defend against evolving cyber threats, ensuring your 
                            data and syste.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/cloud.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Cloud Solutions</h4>
                        <p class="card-text">Embrace the power of the cloud with our virtualization 
                            services. We facilitate migration, management, and optimization of 
                            cloud-based infrastructures, providing scalable, 
                            flexible, and secure solutions..</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/It services.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Managed IT Services</h4>
                        <p class="card-text">Our managed IT services are tailored 
                            to meet your specific business needs. 
                            We offer a range of services that include 
                            IT consulting, network management, and server maintenance..</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/optimisation.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Infrastructure Optimization</h4>
                        <p class="card-text">Enhance efficiency and performance through 
                            infrastructure optimization. We analyze, upgrade, and 
                            streamline your IT infrastructure, maximizing resource 
                            utilization and minimizing downtime.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-img">
                        <img class="img-fluid" src="img/consulting.jpg" alt="" />
                    </div>
                    <div class="card-body">
                        <h4>Consulting and Implementation</h4>
                        <p class="card-text">Leverage our expertise for strategic IT c
                            onsulting and seamless implementation of technology solutions.
                             We guide you through decision-making processes and execute 
                             plans to align with your business objectives.</p>
                    </div>
                </div>
            </div>
           
        </div>
        <!-- /.row -->
    </div>
</div>
<!--End services bar-->

  <!-- Footer Start -->
  <section id="footer">
        <footer class="bg-danger">
            <div class="container">              <div class="row justify-content-center">
                <!-- Quick Links removed as per request -->
                <div class="col-md-6 footer-column text-center">
                  <ul class="nav flex-column">
                    <li class="nav-item">
                      <span class="footer-title">Contact & Support</span>
                    </li>
                    <li class="nav-item d-flex align-items-center justify-content-center">
                      <span class="nav-link me-3" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-phone me-2"></i>+263 8677010124</span>
                      <span class="nav-link" style="display: flex; align-items: center; flex-wrap: wrap;"><i class="fas fa-envelope me-2"></i><EMAIL></span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row justify-content-center mt-3">
                <div class="col-lg-3 col-md-6 text-center">
                    <img src="img/CYVIRNET Logo-06.png" class="img-fluid">
                </div>
              </div>              
            </div>
          </footer>
        <!-- Footer End -->
    
    
          <!--Back to Top-->
            <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
        </div>
</section>
<!-- Footer End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
</body>

</html>
